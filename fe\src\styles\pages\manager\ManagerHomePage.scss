@use '../../base/variables' as vars;
@use '../../base/mixin' as mix;

.manager-home-page {
  @include mix.reset();
  @include mix.body-base();
  width: 100%;
  overflow-x: hidden;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;

  .dashboard-section {
    max-width: 1400px;
    margin: 0 auto;
    padding: vars.$spacing-xl vars.$spacing-base;

    .dashboard-title {
      @include mix.heading(2.5rem, vars.$dark-blue);
      text-align: center;
      margin-bottom: vars.$spacing-xl;
      font-weight: 800;
      text-transform: uppercase;
    }

    .dashboard-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: vars.$spacing-lg;
      margin-bottom: vars.$spacing-xl;

      .stat-card {
        background: #ffffff;
        padding: vars.$spacing-lg;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        text-align: center;
        transition: transform 0.3s ease;

        &:hover {
          transform: translateY(-5px);
        }

        h3 {
          @include mix.heading(1.2rem, #1e293b);
          margin-bottom: vars.$spacing-sm;
          font-weight: 700;
        }

        p {
          @include mix.text(2rem, vars.$secondary-color);
          font-weight: 600;
        }
      }
    }

    .dashboard-content {
      background: #ffffff;
      padding: vars.$spacing-lg;
      border-radius: 15px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      text-align: center;

      p {
        @include mix.text(1.1rem, #64748b);
        line-height: 1.8;
      }
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    .dashboard-section {
      padding: vars.$spacing-lg vars.$spacing-sm;

      .dashboard-title {
        font-size: 2rem;
      }

      .dashboard-stats {
        grid-template-columns: 1fr;
      }

      .dashboard-content {
        padding: vars.$spacing-md;
      }
    }
  }
}