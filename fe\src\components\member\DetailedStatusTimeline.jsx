// React import removed - not needed for functional component
import { Timeline, Card, Typography, Space, Tag } from "antd";
import {
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  FileTextOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import "../../styles/components/DetailedStatusTimeline.scss";

const { Text, Title } = Typography;

/**
 * Component hiển thị timeline chi tiết cho lịch sử hoạt động của Member
 * Hiển thị đầy đủ tiến trình xử lý với thời gian cụ thể
 */
const DetailedStatusTimeline = ({ activity, workflowType = "request" }) => {
  if (!activity) return null;

  // Mapping status names theo yêu cầu UI
  const getStatusDisplayName = (status, type, isFirstStep = false) => {
    if (type === "request") {
      switch (status) {
        case 0: // PENDING
          return isFirstStep ? "Tạo yêu cầu" : "Đang chờ xử lý";
        case 1: // ACCEPTED/APPROVED
          return "Đã chấp nhận";
        case 2: // COMPLETED
          return "Hoàn thành";
        case 3: // REJECTED
          return "Từ chối";
        default:
          return "Không xác định";
      }
    } else {
      // Donation workflow - mapping theo process (1-5)
      switch (status) {
        case 0:
          return "Đăng ký thành công";
        case 1:
          return "Đăng ký";
        case 2:
          return "Khám sức khỏe cơ bản";
        case 3:
          return "Lấy máu";
        case 4:
          return "Xét nghiệm máu";
        case 5:
          return "Nhập kho";
        case -1:
          return "Không đủ điều kiện";
        default:
          return "Không xác định";
      }
    }
  };

  // Get icon cho từng status
  const getStatusIcon = (status, type) => {
    if (type === "request") {
      switch (status) {
        case 0:
          return <ClockCircleOutlined style={{ color: "#faad14" }} />;
        case 1:
          return <CheckCircleOutlined style={{ color: "#52c41a" }} />;
        case 2:
          return <CheckCircleOutlined style={{ color: "#1890ff" }} />;
        case 3:
          return <CloseCircleOutlined style={{ color: "#ff4d4f" }} />;
        default:
          return <ExclamationCircleOutlined style={{ color: "#d9d9d9" }} />;
      }
    } else {
      switch (status) {
        case 0:
        case 1:
        case 2:
        case 3:
          return <CheckCircleOutlined style={{ color: "#52c41a" }} />;
        case 4:
        case 5:
          return <CheckCircleOutlined style={{ color: "#1890ff" }} />;
        case -1:
          return <CloseCircleOutlined style={{ color: "#ff4d4f" }} />;
        default:
          return <ExclamationCircleOutlined style={{ color: "#d9d9d9" }} />;
      }
    }
  };

  // Tạo timeline items từ activity history
  const createTimelineItems = () => {
    const items = [];

    // Thêm thời điểm tạo yêu cầu
    items.push({
      dot: <FileTextOutlined style={{ color: "#1890ff" }} />,
      color: "#1890ff",
      children: (
        <div className="timeline-item">
          <div className="timeline-content">
            <Text strong>
              {workflowType === "donation" ? "Đăng ký hiến máu" : "Tạo yêu cầu"}
            </Text>
            <br />
            <Text type="secondary">
              {dayjs(activity.createdAt).format("HH:mm [ngày] DD/MM/YYYY")}
            </Text>
            {activity.appointmentDate && workflowType === "donation" && (
              <>
                <br />
                <Text type="secondary" style={{ fontSize: "12px" }}>
                  Lịch hẹn: {dayjs(activity.appointmentDate).format("DD/MM/YYYY")} - {activity.timeSlot || "Chưa xác định"}
                </Text>
              </>
            )}
          </div>
        </div>
      ),
    });

    // Cho donation workflow, hiển thị timeline dựa trên process thay vì status
    if (workflowType === "donation") {
      const currentProcess = activity.process || 1; // Default to process 1 if not set
      const currentStatus = activity.rawStatus !== undefined ? activity.rawStatus : activity.status;



      // Định nghĩa các bước quy trình hiến máu theo đúng process workflow
      const donationSteps = [
        { process: 1, name: "Đăng ký", description: "Đăng ký hiến máu thành công" },
        { process: 2, name: "Khám sức khỏe cơ bản", description: "Kiểm tra sức khỏe và đánh giá điều kiện" },
        { process: 3, name: "Lấy máu", description: "Thực hiện lấy máu hiến tặng" },
        { process: 4, name: "Xét nghiệm máu", description: "Kiểm tra chất lượng và an toàn máu" },
        { process: 5, name: "Nhập kho", description: "Lưu trữ máu vào kho bảo quản" }
      ];

      // Hiển thị tất cả các bước quy trình hiến máu
      donationSteps.forEach((step) => {
        const isCurrentStep = step.process === currentProcess && currentStatus !== false && currentProcess < 5;
        const isRejected = currentStatus === false && step.process === 2; // Từ chối ở bước khám sức khỏe
        const isCompleted = step.process < currentProcess || (step.process === currentProcess && currentProcess === 5);
        const isPending = step.process > currentProcess && currentStatus !== false; // Các bước chưa thực hiện

        let icon, color;
        if (isRejected) {
          icon = <CloseCircleOutlined style={{ color: "#ff4d4f" }} />;
          color = "#ff4d4f";
        } else if (isCurrentStep) {
          // Bước hiện tại đang thực hiện
          icon = <ClockCircleOutlined style={{ color: "#faad14" }} />;
          color = "#faad14";
        } else if (isCompleted) {
          // Các bước đã hoàn thành
          icon = <CheckCircleOutlined style={{ color: "#52c41a" }} />;
          color = "#52c41a";
        } else if (isPending) {
          // Các bước chưa thực hiện
          icon = <ClockCircleOutlined style={{ color: "#d9d9d9" }} />;
          color = "#d9d9d9";
        } else {
          // Mặc định
          icon = <ExclamationCircleOutlined style={{ color: "#d9d9d9" }} />;
          color = "#d9d9d9";
        }

        items.push({
          dot: icon,
          color: color,
          children: (
            <div className="timeline-item">
              <div className="timeline-content">
                <Text strong>
                  {isRejected ? "Không đủ điều kiện" : step.name}
                </Text>
                <br />
                <Text type="secondary" style={{ fontSize: "12px" }}>
                  {isRejected ? "Không đạt yêu cầu sức khỏe" : step.description}
                </Text>
                {isCurrentStep && !isRejected && (
                  <>
                    <br />
                    <Tag color="processing" size="small">Đang thực hiện</Tag>
                  </>
                )}
                {isCompleted && !isRejected && (
                  <>
                    <br />
                    <Tag color="success" size="small">
                      {currentProcess === 5 && step.process === 5 ? "Hoàn thành" : "Đã hoàn thành"}
                    </Tag>
                  </>
                )}
                {isPending && !isRejected && (
                  <>
                    <br />
                    <Tag color="default" size="small">Chưa thực hiện</Tag>
                  </>
                )}
                {isRejected && (
                  <>
                    <br />
                    <Tag color="error" size="small">Không đủ điều kiện</Tag>

                    {/* Hiển thị lý do từ chối cho donation */}
                    {(activity.rejectionReason || activity.note || activity.notes) && (
                      <>
                        <br />
                        <div style={{
                          background: "#fff2f0",
                          padding: "8px 12px",
                          borderRadius: "6px",
                          border: "1px solid #ffccc7",
                          marginTop: "4px",
                        }}>
                          <Text type="danger" italic style={{ fontSize: "12px" }}>
                            Lý do từ chối: {activity.rejectionReason || activity.note || activity.notes}
                          </Text>
                        </div>
                      </>
                    )}
                  </>
                )}

                {/* Hiển thị ghi chú thường (chỉ cho bước hiện tại) */}
                {activity.notes && isCurrentStep && (
                  <>
                    <br />
                    <div style={{
                      background: "#f8f9fa",
                      padding: "8px 12px",
                      borderRadius: "6px",
                      border: "1px solid #e9ecef",
                      marginTop: "4px",
                    }}>
                      <Text type="secondary" italic style={{ fontSize: "12px" }}>
                        💬 Ghi chú: {activity.notes}
                      </Text>
                    </div>
                  </>
                )}

                {/* Hiển thị ghi chú bác sĩ (chỉ cho bước hiện tại) */}
                {activity.doctorNotes && isCurrentStep && (
                  <>
                    <br />
                    <div style={{
                      background: "#e8f5e8",
                      padding: "8px 12px",
                      borderRadius: "6px",
                      border: "1px solid #c3e6c3",
                      marginTop: "4px",
                    }}>
                      <Text type="secondary" italic style={{ fontSize: "12px" }}>
                        🩺 Ghi chú bác sĩ: {activity.doctorNotes}
                      </Text>
                    </div>
                  </>
                )}
              </div>
            </div>
          ),
        });
      });
    } else {
      // BƯỚC 3: Xử lý Blood Request Workflow (quy trình yêu cầu máu)
      if (activity.statusHistory && activity.statusHistory.length > 0) {
        
        let shouldStop = false;

        activity.statusHistory.forEach((historyItem, index) => {
          // Nếu đã gặp từ chối ở bước trước, không hiển thị các bước sau
          if (shouldStop) return;

          const isFirstStep = index === 0;
          const statusName = getStatusDisplayName(
            historyItem.status,
            workflowType,
            isFirstStep
          );
          const icon = getStatusIcon(historyItem.status, workflowType);

          items.push({
            dot: icon,
            children: (
              <div className="timeline-item">
                <div className="timeline-content">
                  {/* Tên trạng thái */}
                  <Text strong>{statusName}</Text>

                  {/* Người cập nhật */}
                  {historyItem.updatedBy && (
                    <>
                      <br />
                      <Text type="secondary" style={{ fontSize: "12px" }}>
                        Bởi: {historyItem.updatedBy}
                      </Text>
                    </>
                  )}

                  {/* Ghi chú của bước */}
                  {historyItem.notes && (
                    <>
                      <br />
                      <div style={{
                        background: historyItem.status === 3 ? "#fff2f0" : "#f8f9fa", // Màu đỏ nhạt cho từ chối
                        padding: "8px 12px",
                        borderRadius: "6px",
                        border: historyItem.status === 3 ? "1px solid #ffccc7" : "1px solid #e9ecef",
                        marginTop: "4px",
                      }}>
                        <Text
                          type={historyItem.status === 3 ? "danger" : "secondary"}
                          italic
                          style={{ fontSize: "12px" }}
                        >
                          {historyItem.status === 3 ? "Lý do từ chối: " : "💬 Ghi chú: "}
                          {historyItem.notes}
                        </Text>
                      </div>
                    </>
                  )}
                </div>
              </div>
            ),
          });

          // Nếu status hiện tại là từ chối (3), đánh dấu để dừng
          if (historyItem.status === 3) {
            shouldStop = true;
          }
        });
      } else {
        /**
         * Fallback: Nếu không có statusHistory, tạo timeline từ current status
         * Dùng cho trường hợp dữ liệu cũ hoặc không có lịch sử chi tiết
         */
        const currentStatusName = getStatusDisplayName(
          activity.status,
          workflowType
        );
        const currentIcon = getStatusIcon(activity.status, workflowType);

        items.push({
          dot: currentIcon,
          children: (
            <div className="timeline-item">
              <div className="timeline-content">
                <Text strong>{currentStatusName}</Text>

                {/* Hiển thị lý do từ chối nếu có */}
                {activity.status === 3 && (activity.rejectionReason || activity.note || activity.notes) && (
                  <>
                    <br />
                    <div style={{
                      background: "#fff2f0",
                      padding: "8px 12px",
                      borderRadius: "6px",
                      border: "1px solid #ffccc7",
                      marginTop: "4px",
                    }}>
                      <Text type="danger" italic style={{ fontSize: "12px" }}>
                        Lý do từ chối: {activity.rejectionReason || activity.note || activity.notes}
                      </Text>
                    </div>
                  </>
                )}
              </div>
            </div>
          ),
        });
      }
    }

    return items;
  };

  const timelineItems = createTimelineItems();

  return (
    <Card
      title={
        <Space>
          <ClockCircleOutlined />
          <span>Lịch sử xử lý</span>
        </Space>
      }
      className="detailed-status-timeline"
      size="small"
    >
      <Timeline mode="left" items={timelineItems} className="status-timeline" />

      {/* Hiển thị trạng thái tổng quan */}
      <div className="current-status-summary">
        {workflowType === "donation" ? (
          // Hiển thị trạng thái cho Donation Workflow
          <>
            <Text type="secondary">Quy trình hiện tại: </Text>
            <Tag
              color={
                activity.rawStatus === false || activity.status === false
                  ? "error"        // Bị từ chối
                  : activity.process === 5
                    ? "success"    // Hoàn thành (bước 5)
                    : activity.process >= 2
                      ? "success"    // Chấp nhận (bước 2-4)
                      : "warning"    // Mới đăng ký (bước 1)
              }
            >
              {activity.rawStatus === false || activity.status === false
                ? "Không đủ điều kiện"
                : activity.process === 5
                  ? "Hoàn thành"
                  : activity.process >= 2
                    ? "Chấp nhận"
                    : "Đã đăng ký"
              }
            </Tag>
            {/* Hiển thị số bước hiện tại */}
            {activity.process !== undefined &&
              activity.rawStatus !== false &&
              activity.status !== false && (
                <>
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    (Bước {activity.process}/5)
                  </Text>
                </>
              )}
          </>
        ) : (
          // Hiển thị trạng thái cho Blood Request Workflow
          <>
            <Text type="secondary">Trạng thái hiện tại: </Text>
            <Tag
              color={
                activity.status === 2
                  ? "success"      // Hoàn thành
                  : activity.status === 3
                    ? "error"      // Từ chối
                    : activity.status === 1
                      ? "processing" // Đang xử lý
                      : "warning"    // Chờ xử lý
              }
            >
              {getStatusDisplayName(activity.status, workflowType)}
            </Tag>
          </>
        )}
      </div>


    </Card>
  );
};

/**
 * Export component để sử dụng trong các modal khác
 * - ActivityDetailModal: Modal chi tiết hoạt động
 * - ProcessWorkflowModal: Modal xem tiến trình
 */
export default DetailedStatusTimeline;
