import { useState, useMemo } from "react";

/**
 * Hook dùng chung cho sort theo ngày
 * Sử dụng cho cả trang Tin tức và Tà<PERSON> li<PERSON>u
 */
const useSortByDate = (data = [], initialOrder = "newest") => {
  const [sortOrder, setSortOrder] = useState(initialOrder);

  const sortedData = useMemo(() => {
    if (!Array.isArray(data)) return [];
    
    return [...data].sort((a, b) => {
      // Thử nhiều field có thể chứa ngày tạo
      const dateA = new Date(
        a.createdAt || 
        a.postedAt || 
        a.date || 
        a.created_at ||
        a.posted_at ||
        0
      );
      const dateB = new Date(
        b.createdAt || 
        b.postedAt || 
        b.date ||
        b.created_at ||
        b.posted_at ||
        0
      );
      
      return sortOrder === "newest" ? dateB - dateA : dateA - dateB;
    });
  }, [data, sortOrder]);

  return {
    sortOrder,
    setSortOrder,
    sortedData,
  };
};

export default useSortByDate;
