import React, { useState } from "react";
import { Card, Spin, Alert, Radio, Switch } from "antd";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
  Cell,
  <PERSON><PERSON><PERSON>,
  Pie
} from "recharts";
import { fetchBloodInventory } from "../../services/bloodInventoryService";
import "../../styles/components/BloodInventoryChart.scss";

// Component mapping - cập nhật theo yêu cầu
const BLOOD_COMPONENT_MAP = {
  1: "Hồng cầu",
  2: "Tiểu cầu",
  3: "<PERSON><PERSON>ết tương",
  4: "Máu toàn phần",
};

// Helper function to extract volume from bagType
const getBagVolume = (bagType) => {
  const match = bagType.match(/(\d+)ml/);
  return match ? parseInt(match[1]) : 0;
};

/**
 * Component biểu đồ kho máu tái sử dụng
 * Hỗ trợ nhiều loại biểu đồ và đơn vị thống kê
 * Sử dụng dữ liệu thật từ API BloodInventory
 */
const BloodInventoryChart = ({
  title = "Thống kê kho máu",
  height = 400,
  groupBy = "bloodType", // "bloodType", "component", "bagType"
  chartType = "bar", // "bar", "pie"
  showUnitToggle = true
}) => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [rawData, setRawData] = useState([]);
  const [unit, setUnit] = useState("bags"); // "bags" or "volume"

  React.useEffect(() => {
    fetchData();
  }, []);

  React.useEffect(() => {
    if (rawData.length > 0) {
      const transformedData = transformApiData(rawData, groupBy, unit);
      setData(transformedData);
    }
  }, [rawData, groupBy, unit]);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetchBloodInventory();

      console.log("Raw API data:", response);
      setRawData(response);

      // Transform API data to chart format
      const transformedData = transformApiData(response, groupBy, unit);
      setData(transformedData);
    } catch (err) {
      console.error("Error fetching blood inventory data:", err);
      setError("Không thể tải dữ liệu biểu đồ");
    } finally {
      setLoading(false);
    }
  };

  // Transform API data from BloodInventory API
  // API format: { "inventoryID": 1, "bloodGroup": "A", "rhType": "Rh+", "componentId": 4, "bagType": "450ml", "quantity": 10, "status": 0, "isRare": false, "lastUpdated": "2025-07-24T20:14:00.967" }
  const transformApiData = (apiData, groupByType = "bloodType", unitType = "bags") => {
    if (!apiData || !Array.isArray(apiData)) return [];

    console.log("Transforming data with groupBy:", groupByType, "unit:", unitType);

    const groupedData = {};

    apiData.forEach(item => {
      let key, displayName, additionalInfo = {};

      // Calculate volume for this item
      const bagVolume = getBagVolume(item.bagType);
      const itemVolume = item.quantity * bagVolume;

      switch (groupByType) {
        case "bloodType":
          key = `${item.bloodGroup}${item.rhType}`;
          displayName = key;
          additionalInfo = {
            bloodGroup: item.bloodGroup,
            rhType: item.rhType,
            isRare: item.isRare
          };
          break;

        case "component":
          key = item.componentId.toString();
          displayName = BLOOD_COMPONENT_MAP[item.componentId] || `Component ${item.componentId}`;
          additionalInfo = {
            componentId: item.componentId,
            componentName: displayName
          };
          break;

        case "bagType":
          key = item.bagType;
          displayName = item.bagType;
          additionalInfo = {
            bagType: item.bagType,
            bagVolume: bagVolume
          };
          break;

        default:
          key = `${item.bloodGroup}${item.rhType}`;
          displayName = key;
          additionalInfo = {
            bloodGroup: item.bloodGroup,
            rhType: item.rhType,
            isRare: item.isRare
          };
      }

      if (groupedData[key]) {
        groupedData[key].totalBags += item.quantity; // quantity là số túi
        groupedData[key].totalVolume += itemVolume; // tổng thể tích
        groupedData[key].itemCount += 1; // số dòng dữ liệu
      } else {
        groupedData[key] = {
          name: displayName,
          key: key,
          totalBags: item.quantity, // quantity là số túi
          totalVolume: itemVolume, // tổng thể tích
          itemCount: 1, // số dòng dữ liệu
          ...additionalInfo
        };
      }
    });

    // Set value based on unit type
    const result = Object.values(groupedData).map(item => ({
      ...item,
      value: unitType === "volume" ? item.totalVolume : item.totalBags,
      quantity: item.totalBags, // for backward compatibility
    })).sort((a, b) => b.value - a.value);

    console.log("Transformed chart data:", result);
    return result;
  };

  // Colors for different group types
  const BLOOD_GROUP_COLORS = {
    "ARh+": "#D93E4C", "A+": "#D93E4C",
    "ARh-": "#B71C1C", "A-": "#B71C1C",
    "BRh+": "#1976D2", "B+": "#1976D2",
    "BRh-": "#0D47A1", "B-": "#0D47A1",
    "ABRh+": "#7B1FA2", "AB+": "#7B1FA2",
    "ABRh-": "#4A148C", "AB-": "#4A148C",
    "ORh+": "#F57C00", "O+": "#F57C00",
    "ORh-": "#E65100", "O-": "#E65100",
  };

  const COMPONENT_COLORS = {
    "Toàn phần": "#D93E4C",
    "Hồng cầu": "#1976D2",
    "Huyết tương": "#F57C00",
    "Tiểu cầu": "#7B1FA2",
  };

  const BAG_TYPE_COLORS = {
    "250ml": "#52c41a",
    "350ml": "#1890ff",
    "450ml": "#722ed1",
  };

  const getColor = (item) => {
    if (groupBy === "component") {
      return COMPONENT_COLORS[item.name] || "#8884d8";
    } else if (groupBy === "bagType") {
      return BAG_TYPE_COLORS[item.name] || "#8884d8";
    } else {
      return BLOOD_GROUP_COLORS[item.name] || "#8884d8";
    }
  };

  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;

      return (
        <div className="custom-tooltip">
          <p className="tooltip-label">{data.name}</p>
          <p className="tooltip-bags">{`Số túi: ${data.totalBags.toLocaleString()}`}</p>
          <p className="tooltip-volume">{`Thể tích: ${data.totalVolume.toLocaleString()} ml`}</p>
          {data.isRare && <p className="tooltip-rare">⚠️ Nhóm máu hiếm</p>}
        </div>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <Card className="blood-inventory-chart">
        <div className="chart-loading">
          <Spin size="large" />
          <p>Đang tải dữ liệu biểu đồ...</p>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="blood-inventory-chart">
        <Alert
          message="Lỗi tải dữ liệu"
          description={error}
          type="error"
          showIcon
          action={
            <button onClick={fetchData} className="retry-button">
              Thử lại
            </button>
          }
        />
      </Card>
    );
  }

  const getYAxisLabel = () => {
    return unit === "volume" ? "Thể tích (ml)" : "Số lượng túi";
  };

  const renderChart = () => {
    if (chartType === "pie" && groupBy === "bloodType") {
      return (
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(1)}%`}
            outerRadius={120}
            fill="#8884d8"
            dataKey="value"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={getColor(entry)} />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
          <Legend />
        </PieChart>
      );
    }

    return (
      <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 80 }}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis
          dataKey="name"
          angle={-45}
          textAnchor="end"
          height={80}
          fontSize={12}
        />
        <YAxis
          label={{ value: getYAxisLabel(), angle: -90, position: 'insideLeft' }}
          fontSize={12}
        />
        <Tooltip content={<CustomTooltip />} />
        <Bar dataKey="value" name={getYAxisLabel()} radius={[4, 4, 0, 0]}>
          {data.map((entry, index) => (
            <Cell
              key={`cell-${index}`}
              fill={getColor(entry)}
            />
          ))}
        </Bar>
      </BarChart>
    );
  };

  return (
    <Card className="blood-inventory-chart">
      <div className="chart-header">
        <h3>{title}</h3>
        {showUnitToggle && (
          <div className="chart-controls">
            <span style={{ marginRight: 8 }}>Đơn vị:</span>
            <Radio.Group
              value={unit}
              onChange={(e) => setUnit(e.target.value)}
              size="small"
            >
              <Radio.Button value="bags">Số túi</Radio.Button>
              <Radio.Button value="volume">Thể tích (ml)</Radio.Button>
            </Radio.Group>
          </div>
        )}
      </div>

      <div className="chart-content">
        <ResponsiveContainer width="100%" height={height}>
          {renderChart()}
        </ResponsiveContainer>
      </div>

      {/* Summary info */}
      <div className="chart-summary">
        <div className="summary-item">
          <span className="summary-label">Tổng số mục:</span>
          <span className="summary-value">{data.length}</span>
        </div>
        <div className="summary-item">
          <span className="summary-label">Tổng số túi:</span>
          <span className="summary-value">
            {data.reduce((sum, item) => sum + item.totalBags, 0).toLocaleString()}
          </span>
        </div>
        <div className="summary-item">
          <span className="summary-label">Tổng thể tích:</span>
          <span className="summary-value">
            {data.reduce((sum, item) => sum + item.totalVolume, 0).toLocaleString()} ml
          </span>
        </div>
      </div>
    </Card>
  );
};

export default BloodInventoryChart;
