import { useState, useEffect } from "react";
import hospitalInfoService from "../services/hospitalInfoService";

/**
 * Hook để lấy và cache thông tin bệnh viện
 * Sử dụng trong tất cả các component cần hiển thị thông tin bệnh viện
 */
const useHospitalInfo = () => {
  const [hospitalInfo, setHospitalInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchHospitalInfo();
  }, []);

  const fetchHospitalInfo = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await hospitalInfoService.getHospitalInfo();
      setHospitalInfo(data);
    } catch (err) {
      console.error("Error in useHospitalInfo:", err);
      setError("<PERSON>hông thể tải thông tin bệnh viện");
      // Set fallback data
      setHospitalInfo({
        id: 1,
        name: "Trung Tâm Hiến Máu",
        address: "đường CMT8, Q.3, TP.HCM, Vietnam",
        phone: "02838554137",
        email: "<EMAIL>",
        workingHours: "Thứ 2 - Thứ 6: 7:00 - 17:00",
        mapImageUrl: "https://www.google.com/maps/place/10%C2%B046'30.5%22N+106%C2%B041'10.4%22E/@10.7751389,106.6862222,17z/data=!3m1!4b1!4m4!3m3!8m2!3d10.7751389!4d106.6862222?entry=ttu&g_ep=EgoyMDI1MDUyOC4wIKXMDSoASAFQAw%3D%3D",
        latitude: 10.7751237,
        longitude: 106.6862143
      });
    } finally {
      setLoading(false);
    }
  };

  return {
    hospitalInfo,
    loading,
    error,
    refetch: fetchHospitalInfo
  };
};

export default useHospitalInfo;
