import React, { useState } from "react";
import { Mo<PERSON>, <PERSON><PERSON>, Steps, Tag, Row, Col, Divider } from "antd";
import {
  UserOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  HeartOutlined,
} from "@ant-design/icons";
import bloodDonationService from "../../services/bloodDonationService";
import { toast } from "../../utils/toastUtils";

const { Step } = Steps;

// Donation process steps (1-5)
const DONATION_PROCESS = {
  REGISTERED: 1,           // Đăng ký
  HEALTH_CHECKED: 2,       // Khám sức khỏe cơ bản
  BLOOD_TAKEN: 3,          // Lấy máu
  BLOOD_TESTED: 4,         // Xét nghiệm máu
  STORED: 5,               // Nhập kho
};

// Legacy statuses for backward compatibility
const DONATION_STATUSES = {
  REGISTERED: "registered",
  HEALTH_CHECKED: "health_checked",
  BLOOD_TAKEN: "blood_taken",
  BLOOD_TESTED: "blood_tested",
  STORED: "stored",
  NOT_ELIGIBLE: "not_eligible"
};

const ProcessWorkflowModal = ({
  visible,
  onCancel,
  selectedItem,
  onStoreBlood,
  isManager = false,
  title = "Quy trình hiến máu",
  onRefresh, // Add callback to refresh data after process update
}) => {
  const [updating, setUpdating] = useState(false);
  // Get status info for display based on process number (1-5) and status boolean
  const getStatusInfo = (item) => {
    const process = item.process || 1;
    const status = item.status;

    // If rejected (status = false), show rejection
    if (status === false) {
      return {
        text: "Không chấp nhận",
        color: "#ff4d4f",
        icon: <ExclamationCircleOutlined />,
        step: -1,
      };
    }

    // Map process steps to display info
    const processMap = {
      1: {
        text: "Đăng ký",
        color: "#1890ff",
        icon: <UserOutlined />,
        step: 0,
      },
      2: {
        text: "Khám sức khỏe cơ bản",
        color: "#fa8c16",
        icon: <CheckCircleOutlined />,
        step: 1,
      },
      3: {
        text: "Lấy máu",
        color: "#722ed1",
        icon: <HeartOutlined />,
        step: 2,
      },
      4: {
        text: "Xét nghiệm máu",
        color: "#13c2c2",
        icon: <ClockCircleOutlined />,
        step: 3,
      },
      5: {
        text: "Nhập kho",
        color: "#52c41a",
        icon: <CheckCircleOutlined />,
        step: 4,
      },
    };

    return processMap[process] || processMap[1];
  };

  // Get donation process steps (removed step 4 - "Hoàn thành")
  const getDonationSteps = () => [
    {
      title: "Đăng ký",
      description: "Đăng ký hiến máu",
      icon: <UserOutlined />,
    },
    {
      title: "Khám sức khỏe cơ bản",
      description: "Kiểm tra sức khỏe và đánh giá điều kiện",
      icon: <CheckCircleOutlined />,
    },
    {
      title: "Lấy máu",
      description: "Thực hiện lấy máu hiến tặng",
      icon: <HeartOutlined />,
    },
    {
      title: "Xét nghiệm máu",
      description: "Kiểm tra chất lượng và an toàn máu",
      icon: <ClockCircleOutlined />,
    },
    {
      title: "Nhập kho",
      description: "Lưu trữ máu vào kho bảo quản",
      icon: <CheckCircleOutlined />,
    },
  ];

  const handleStoreBlood = () => {
    if (onStoreBlood && selectedItem) {
      onStoreBlood(selectedItem.id);
      onCancel();
    }
  };

  // Handle updating appointment process using PATCH /api/Appointment/{id}/process/{process}
  const handleUpdateProcess = async (nextProcess) => {
    if (!selectedItem?.id) return;

    setUpdating(true);
    try {
      await bloodDonationService.updateAppointmentProcess(selectedItem.id, nextProcess);
      toast.success(`Cập nhật quy trình thành công!`);

      // Refresh data if callback provided
      if (onRefresh) {
        onRefresh();
      }

      onCancel();
    } catch (error) {
      console.error("Error updating appointment process:", error);
      toast.error("Cập nhật quy trình thất bại!");
    } finally {
      setUpdating(false);
    }
  };

  // Get next process step
  const getNextProcess = (currentProcess) => {
    const current = currentProcess || 1;
    return Math.min(current + 1, 5); // Max process is 5 (stored)
  };

  // Check if can advance to next process
  const canAdvanceProcess = (item) => {
    const currentProcess = item.process || 1;
    // Don't show advance button when already at final step (process = 5) or rejected
    return currentProcess < 4 && item.status !== false;
  };

  if (!selectedItem) return null;

  // Get the name field - could be donorName or name depending on the data structure
  const itemName = selectedItem.donorName || selectedItem.name;
  const statusInfo = getStatusInfo(selectedItem);

  return (
    <Modal
      title={`${title}: ${itemName}`}
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="close" onClick={onCancel}>
          Đóng
        </Button>,
        // Show advance process button if can advance and user is manager
        isManager && canAdvanceProcess(selectedItem) && (
          <Button
            key="advance"
            type="default"
            loading={updating}
            onClick={() => handleUpdateProcess(getNextProcess(selectedItem.process))}
          >
            Tiến hành bước tiếp theo
          </Button>
        ),
        // Show store blood button when process is 4 (blood tested) and status is true
        selectedItem.process === 4 && selectedItem.status === true && (
          <Button key="store" type="primary" onClick={handleStoreBlood}>
            Nhập kho
          </Button>
        ),
      ]}
      width={800}
    >
      <div className="process-workflow">
        <div className="donor-summary">
          <Row gutter={16}>
            <Col span={8}>
              <div className="summary-item">
                <strong>Người hiến:</strong> {itemName}
              </div>
            </Col>
            <Col span={8}>
              <div className="summary-item">
                <strong>Nhóm máu:</strong>
                <Tag color="#D93E4C" style={{ marginLeft: 8 }}>
                  {selectedItem.bloodType}
                </Tag>
              </div>
            </Col>
            <Col span={8}>
              <div className="summary-item">
                <strong>Ngày hẹn:</strong>{" "}
                {selectedItem.appointmentDate
                  ? new Date(selectedItem.appointmentDate).toLocaleDateString(
                    "vi-VN"
                  )
                  : selectedItem.registrationDate
                    ? new Date(selectedItem.registrationDate).toLocaleDateString(
                      "vi-VN"
                    )
                    : "Chưa có"}
              </div>
            </Col>
          </Row>
        </div>

        <Divider />

        <div className="workflow-steps">
          {selectedItem.status === false ? (
            // Show rejection status
            <div className="rejection-status">
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <ExclamationCircleOutlined style={{ fontSize: '48px', color: '#ff4d4f', marginBottom: '16px' }} />
                <h3 style={{ color: '#ff4d4f' }}>Không chấp nhận</h3>
                {selectedItem.notes && (
                  <p style={{ color: '#666', marginTop: '8px' }}>
                    <strong>Lý do:</strong> {selectedItem.notes}
                  </p>
                )}
              </div>
            </div>
          ) : (
            // Show normal process steps
            <Steps
              current={selectedItem.process >= 4 ? 4 : selectedItem.process}
              status={selectedItem.process === 5 ? "finish" : "process"}
              direction="vertical"
              size="small"
            >
              {getDonationSteps().map((step, index) => (
                <Step
                  key={index}
                  title={step.title}
                  description={step.description}
                  icon={step.icon}
                />
              ))}
            </Steps>
          )}
        </div>

        {/* Show action section based on current process */}
        {isManager && selectedItem.status !== false && selectedItem.process < 5 && (
          <div className="action-section">
            <Divider />
            <div className="action-info">
              <h4>Hành động có thể thực hiện</h4>
              {selectedItem.process === 1 && (
                <p>Tiến hành khám sức khỏe cơ bản cho người hiến máu.</p>
              )}
              {selectedItem.process === 2 && (
                <p>Thực hiện lấy máu từ người hiến máu.</p>
              )}
              {selectedItem.process === 3 && (
                <p>Tiến hành xét nghiệm máu để kiểm tra chất lượng và an toàn.</p>
              )}
              {selectedItem.process === 4 && selectedItem.status === true && (
                <p>
                  Máu đã được xét nghiệm và đạt tiêu chuẩn chất lượng. Bạn có thể nhập
                  vào kho máu để hoàn thành quy trình.
                </p>
              )}
            </div>
          </div>
        )}

        {/* Show completion message when process is finished */}
        {selectedItem.process === 5 && selectedItem.status !== false && (
          <div className="completion-section">
            <Divider />
            <div className="completion-info">
              <h4 style={{ color: "#52c41a" }}>Quy trình hoàn thành</h4>
              <p style={{ color: "#52c41a" }}>
                Máu đã được nhập kho thành công. Quy trình hiến máu đã hoàn tất.
              </p>
            </div>
          </div>
        )}

        {selectedItem.notes && (
          <div className="notes-section">
            <Divider />
            <h4>Ghi chú</h4>
            <p>{selectedItem.notes}</p>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default ProcessWorkflowModal;
export { DONATION_STATUSES, DONATION_PROCESS };
