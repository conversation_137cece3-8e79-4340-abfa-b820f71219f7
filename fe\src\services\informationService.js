import axiosInstance from "./axiosInstance";

const API_URL = import.meta.env.VITE_INFORMATION_API;

/**
 * Service for user information management
 * Handles profile updates and password changes
 */
const informationService = {
  /**
   * Get current user information
   * @returns {Promise} Promise containing user information
   */
  getUserInfo: async () => {
    try {
      console.log("Fetching user info from:", API_URL);
      const response = await axiosInstance.get(API_URL);
      console.log("User info response:", response.data);
      return response.data;
    } catch (error) {
      console.error("Error fetching user info:", error);
      throw error;
    }
  },

  /**
   * Update user information
   * @param {Object} userInfo - User information to update
   * @returns {Promise} Promise containing update result
   */
  updateUserInfo: async (userInfo) => {
    try {
      console.log("Updating user info:", userInfo);
      const response = await axiosInstance.put(API_URL, userInfo);
      console.log("Update user info response:", response.data);
      return response.data;
    } catch (error) {
      console.error("Error updating user info:", error);
      throw error;
    }
  },

  /**
   * Change user password
   * @param {Object} passwordData - Password change data
   * @param {string} passwordData.currentPassword - Current password
   * @param {string} passwordData.newPassword - New password
   * @returns {Promise} Promise containing password change result
   */
  changePassword: async (passwordData) => {
    try {
      console.log("Changing password...");
      const response = await axiosInstance.post(`${API_URL}/change-password`, passwordData);
      console.log("Change password response:", response.data);
      return response.data;
    } catch (error) {
      console.error("Error changing password:", error);
      throw error;
    }
  },

  /**
   * Get user information by ID
   * @param {string|number} userId - User ID
   * @returns {Promise} Promise containing user information
   */
  getUserInfoById: async (userId) => {
    try {
      console.log("Fetching user info by ID:", userId);
      const response = await axiosInstance.get(`${API_URL}/${userId}`);
      console.log("User info by ID response:", response.data);
      return response.data;
    } catch (error) {
      console.error("Error fetching user info by ID:", error);
      throw error;
    }
  },

  /**
   * Update user information by ID
   * @param {string|number} userId - User ID
   * @param {Object} userInfo - User information to update
   * @returns {Promise} Promise containing update result
   */
  updateUserInfoById: async (userId, userInfo) => {
    try {
      console.log("Updating user info by ID:", userId, userInfo);
      const response = await axiosInstance.put(`${API_URL}/${userId}`, userInfo);
      console.log("Update user info by ID response:", response.data);
      return response.data;
    } catch (error) {
      console.error("Error updating user info by ID:", error);
      if (error.response) {
        console.error("Response status:", error.response.status);
        console.error("Response data:", error.response.data);
        console.error("Response headers:", error.response.headers);

        // Log chi tiết validation errors
        if (error.response.data?.errors) {
          console.error("Validation errors:", error.response.data.errors);
          Object.keys(error.response.data.errors).forEach(field => {
            console.error(`Validation error for ${field}:`, error.response.data.errors[field]);
          });
        }
      }
      throw error;
    }
  },
};

// Export individual functions for easier importing
export const getUserInfo = informationService.getUserInfo;
export const updateUserInfo = informationService.updateUserInfo;
export const changePassword = informationService.changePassword;
export const getUserInfoById = informationService.getUserInfoById;
export const updateUserInfoById = informationService.updateUserInfoById;

export default informationService;
