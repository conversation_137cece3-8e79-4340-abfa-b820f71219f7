import { apiClient } from "./axiosInstance";

class BloodDonationService {
  async getUserInfo(userId) {
    try {
      const response = await apiClient.get(`/Information/${userId}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async getAllBloodDonationSubmissions() {
    try {
      const response = await apiClient.get("/Appointment");
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async createBloodDonationSubmission(donationData) {
    try {
      const response = await apiClient.post("/Appointment", donationData);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async getBloodDonationSubmissionById(appointmentId) {
    try {
      const response = await apiClient.get(`/Appointment/${appointmentId}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async deleteBloodDonationSubmission(appointmentId) {
    try {
      const response = await apiClient.delete(`/Appointment/${appointmentId}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async updateBloodDonationSubmissionStatus(appointmentId, status) {
    try {
      const response = await apiClient.put(
        `/Appointment/${appointmentId}/status`,
        {
          status: status,
        }
      );
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  calculateAge(dateOfBirth) {
    if (!dateOfBirth) return 0;
    const dob = new Date(dateOfBirth);
    if (isNaN(dob)) return 0;
    const today = new Date();
    let age = today.getFullYear() - dob.getFullYear();
    const m = today.getMonth() - dob.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < dob.getDate())) {
      age--;
    }
    return age;
  }

  async getAppointmentsByUser(userId) {
    try {
      // First try to get user info which might contain appointment history
      try {
        const userInfo = await this.getUserInfo(userId);

        // Check if user info contains blood donation history
        if (
          userInfo.bloodDonationHistory &&
          Array.isArray(userInfo.bloodDonationHistory)
        ) {
          return userInfo.bloodDonationHistory;
        }
      } catch (userError) {
        // User info endpoint not available, trying blood donation endpoints
      }

      // Try multiple blood donation endpoints with includeCancelled parameter
      const possibleEndpoints = [
        `/Appointment?includeCancelled=true`, // Get all including cancelled
        `/Appointment`, // Get all and filter
        `/Appointment?userId=${userId}&includeCancelled=true`,
        `/Appointment?userId=${userId}`,
        `/Appointment?userID=${userId}&includeCancelled=true`,
        `/Appointment?userID=${userId}`,
        `/Appointment/user/${userId}?includeCancelled=true`,
        `/Appointment/user/${userId}`,
        `/Appointment/by-user/${userId}?includeCancelled=true`,
        `/Appointment/by-user/${userId}`,
        `/appointments/user/${userId}`,
      ];

      let lastError = null;

      for (const endpoint of possibleEndpoints) {
        try {
          const response = await apiClient.get(endpoint);

          if (Array.isArray(response.data)) {
            const userAppointments = response.data.filter(
              (appointment) =>
                appointment.userId === parseInt(userId) ||
                appointment.userID === parseInt(userId) ||
                appointment.UserId === parseInt(userId) ||
                appointment.UserID === parseInt(userId)
            );




            return userAppointments;
          }

          return response.data;
        } catch (error) {
          lastError = error;
          continue;
        }
      }

      // If all endpoints fail, throw the last error
      throw lastError;
    } catch (error) {
      throw error;
    }
  }



  async getAppointmentDetails(appointmentId) {
    try {
      const response = await apiClient.get(`/Appointment/${appointmentId}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async getLastDonation(userId) {
    try {
      const response = await apiClient.get(
        `/Appointment/last-donation/${userId}`
      );
      return response.data;
    } catch (error) {
      // Handle specific error cases
      if (error.response?.status === 500) {
        // 500 error might indicate no previous donations, SQL null value issue, or NullReferenceException
        return null; // Return null instead of throwing error
      } else if (error.response?.status === 404) {
        // 404 means no donation found
        return null;
      } else if (error.response?.data && typeof error.response.data === 'string' &&
        error.response.data.includes('NullReferenceException')) {
        // Handle NullReferenceException from backend
        return null;
      }

      // For other errors, still throw
      throw error;
    }
  }

  async updateSelfReportedDonation(userId, lastDonationDate) {
    try {
      // Based on Swagger API documentation, the endpoint expects direct JSON format
      let requestBody;

      if (lastDonationDate !== null && lastDonationDate !== "") {
        // User has donated before and provided a date
        // API expects format: "2025-07-24T15:03:46.335Z"
        const date = new Date(`${lastDonationDate}T00:00:00.000Z`);
        const isoString = date.toISOString();
        requestBody = {
          selfReportedLastDonationDate: isoString,
        };
      } else {
        // User chose "Không" (never donated)
        // Try sending a very old date instead of null (some APIs don't accept null)
        const veryOldDate = new Date('1900-01-01T00:00:00.000Z').toISOString();
        requestBody = {
          selfReportedLastDonationDate: veryOldDate,
        };
      }

      // Use PUT method as per API documentation
      const response = await apiClient.put(
        `/Information/${userId}/self-reported-donation`,
        requestBody
      );

      return response.data;
    } catch (error) {
      throw error; // Re-throw to handle in calling function
    }
  }

  /**
   * Create new blood donation appointment
   * @param {Object} appointmentData - Appointment data
   * @returns {Promise} API response
   */
  async createAppointment(appointmentData) {
    try {
      // Add a flag to prevent multiple appointment creation
      if (this._creatingAppointment) {
        throw new Error("Appointment creation already in progress");
      }
      this._creatingAppointment = true;

      // Validate userId before sending
      if (!appointmentData.userId || appointmentData.userId === 0) {
        throw new Error("Invalid userId in appointment data");
      }



      // Try adding userId to headers as well in case backend reads from there
      const headers = {
        'X-User-ID': appointmentData.userId.toString(),
        'X-UserId': appointmentData.userId.toString(),
        'User-ID': appointmentData.userId.toString(),
        'UserId': appointmentData.userId.toString(),
        'user-id': appointmentData.userId.toString(),
        'userid': appointmentData.userId.toString(),
        'x-userid': appointmentData.userId.toString(),
        'X-Skip-Notification': 'true', // Try to skip notification logging
        'X-Disable-Notification': 'true',
        'Skip-Notification': 'true',
        'X-No-Notification': 'true',
        'No-Notification': 'true',
        'no-notification': 'true',
        'Disable-Logging': 'true',
        'disable-logging': 'true',
        'X-Bypass-Notification': 'true',
        'x-bypass-notification': 'true',
        'x-skip-notification': 'true',
        'x-disable-notification': 'true',
        'x-no-notification': 'true',
        'skip-notification': 'true'
      };



      // Use only the standard endpoint - other endpoints don't exist
      const endpoint = `/Appointment`;

      // Create a simple appointment creation without notification logging
      // Since the backend has a bug with NotificationLog.NotiLog getting UserId = 0
      // Let's try to create appointment with minimal data first
      const simplePayload = {
        userId: parseInt(appointmentData.userId),
        appointmentDate: appointmentData.appointmentDate,
        timeSlot: appointmentData.timeSlot,
        process: appointmentData.process || 0,
        status: appointmentData.status || 0
      };

      // Validate that userId is definitely not 0
      if (simplePayload.userId === 0 || !simplePayload.userId) {
        throw new Error("UserId is 0 - cannot create appointment");
      }

      // Try different payload formats
      const payloadVariations = [
        simplePayload, // Simple payload first
        // Try with explicit integer userId
        {
          ...appointmentData,
          userId: parseInt(appointmentData.userId), // Ensure userId is integer
          UserId: parseInt(appointmentData.userId),
          UserID: parseInt(appointmentData.userId),
          skipNotification: true, // Try to skip notification
          disableNotification: true,
          noNotification: true
        },
        appointmentData, // Original format
        { dto: appointmentData }, // Wrapped in dto
        { appointmentData }, // Wrapped in appointmentData
        { ...appointmentData, UserId: appointmentData.userId }, // Add UserId field
        { ...appointmentData, UserID: appointmentData.userId }, // Add UserID field (uppercase)
        { ...appointmentData, user_id: appointmentData.userId }, // Add user_id field (snake_case)
        // Try with status as different values
        { ...appointmentData, status: 1 }, // Try status = 1
        { ...appointmentData, status: "active" }, // Try status as string
        { ...appointmentData, status: undefined }, // Try without status
      ];

      let response;
      let lastError;

      // First, try a direct API call with minimal headers to avoid notification issues
      try {
        const minimalHeaders = {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem("authToken")}`
        };

        response = await apiClient.post(endpoint, simplePayload, { headers: minimalHeaders });
        this._creatingAppointment = false; // Reset flag on success
        return response.data;
      } catch (error) {

        lastError = error;
      }

      // If direct call failed, try each payload variation with full headers
      for (let payloadIndex = 0; payloadIndex < payloadVariations.length; payloadIndex++) {
        try {
          const payload = payloadVariations[payloadIndex];

          response = await apiClient.post(endpoint, payload, { headers });

          this._creatingAppointment = false; // Reset flag on success
          return response.data;
        } catch (error) {

          lastError = error;
        }
      }

      this._creatingAppointment = false; // Reset flag on failure
      throw lastError;
    } catch (error) {
      this._creatingAppointment = false; // Reset flag on error
      throw error;
    }
  }

  async updateAppointmentStatus(appointmentId, status, notes = null, userId = null) {
    try {
      // Validate and convert parameters
      const validAppointmentId = parseInt(appointmentId);

      // Convert status to boolean: 1 = false (rejected), 2 = true (approved)
      const validStatus = status === 2 || status === "2" ? true : false;



      // Validate parameters
      if (isNaN(validAppointmentId) || validAppointmentId <= 0) {
        throw new Error(`Invalid appointmentId: ${appointmentId}`);
      }

      // API expects boolean status parameter
      // Add headers to try to bypass notification logging issue
      const headers = {
        'X-Skip-Notification': 'true',
        'X-Disable-Notification': 'true',
        'Skip-Notification': 'true',
        'X-No-Notification': 'true',
        'No-Notification': 'true',
        'no-notification': 'true',
        'Disable-Logging': 'true',
        'disable-logging': 'true',
        'X-Bypass-Notification': 'true',
        'x-bypass-notification': 'true',
        'x-skip-notification': 'true',
        'x-disable-notification': 'true',
        'x-no-notification': 'true',
        'skip-notification': 'true'
      };

      // Add userId to headers if available to help backend identify correct user
      if (userId) {
        headers['X-User-ID'] = userId.toString();
        headers['User-ID'] = userId.toString();
        headers['user-id'] = userId.toString();
        headers['userId'] = userId.toString();
        headers['x-user-id'] = userId.toString();
      }

      // Prepare request body with notes if provided
      const requestBody = notes ? { notes } : {};

      // Build URL with query parameters
      let url = `/Appointment/${validAppointmentId}/status/${validStatus}?skipNotification=true&noLog=true`;
      if (notes) {
        // Also try sending notes as query parameter in case backend expects it there
        url += `&notes=${encodeURIComponent(notes)}`;
      }

      // Try with query parameters to bypass notification
      const response = await apiClient.patch(url, requestBody, { headers });
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Doctor update appointment with health information (Stage 1: Health Examination)
  async doctorHealthExaminationUpdate(appointmentId, updateData) {
    try {
      const validAppointmentId = parseInt(appointmentId);
      if (isNaN(validAppointmentId) || validAppointmentId <= 0) {
        throw new Error(`Invalid appointmentId: ${appointmentId}`);
      }

      // Prepare payload for health examination stage (DoctorId1) according to API spec
      const payload = {
        doctorId: updateData.doctorId || updateData.doctorId1 || 4, // Health examination doctor
        process: updateData.process || 2, // Default to health examination process
        status: updateData.status !== undefined ? updateData.status : true // Default to true if not specified
      };

      // Health examination stage fields (DoctorId1) - notes handled separately
      if (updateData.notes !== undefined) payload.notes = updateData.notes;
      if (updateData.bloodPressure !== undefined) payload.bloodPressure = updateData.bloodPressure;
      if (updateData.heartRate !== undefined) payload.heartRate = updateData.heartRate;
      if (updateData.hemoglobin !== undefined) payload.hemoglobin = updateData.hemoglobin;
      if (updateData.temperature !== undefined) payload.temperature = updateData.temperature;
      if (updateData.donationCapacity !== undefined) payload.donationCapacity = updateData.donationCapacity;

      // API expects weightAppointment and heightAppointment fields for health check
      if (updateData.weight !== undefined && updateData.weight > 0) {
        payload.weightAppointment = updateData.weight;
      } else if (updateData.weightAppointment !== undefined && updateData.weightAppointment > 0) {
        payload.weightAppointment = updateData.weightAppointment;
      }

      if (updateData.height !== undefined && updateData.height > 0) {
        payload.heightAppointment = updateData.height;
      } else if (updateData.heightAppointment !== undefined && updateData.heightAppointment > 0) {
        payload.heightAppointment = updateData.heightAppointment;
      }

      // Debug: Log the payload being sent
      console.log('🩺 Health Examination Update Payload:', JSON.stringify(payload, null, 2));

      // Use the correct API endpoint for health check
      const response = await apiClient.put(
        `/Appointment/doctor-health-check/${validAppointmentId}`,
        payload
      );

      return response.data;
    } catch (error) {
      // Enhanced error logging for debugging
      console.error('❌ Health Examination Update Error:', {
        appointmentId: appointmentId,
        payload: payload,
        error: error.response?.data || error.message,
        status: error.response?.status,
        statusText: error.response?.statusText
      });

      // Throw a more descriptive error
      const errorMessage = error.response?.data?.message || error.response?.data || error.message;
      throw new Error(`Health examination update failed: ${errorMessage}`);
    }
  }

  // Doctor update appointment with blood test results (Stage 2: Blood Testing)
  async doctorBloodTestingUpdate(appointmentId, updateData) {
    try {
      const validAppointmentId = parseInt(appointmentId);
      if (isNaN(validAppointmentId) || validAppointmentId <= 0) {
        throw new Error(`Invalid appointmentId: ${appointmentId}`);
      }

      // Prepare payload for blood testing stage (DoctorId2)
      // Try to match the expected API structure more closely
      const payload = {
        doctorId: updateData.doctorId || updateData.doctorId2 || 4 // Blood testing doctor
      };

      // Blood testing stage fields (DoctorId2) - notes handled separately
      // Check if we have blood group from bloodType field (format: "A+", "B-", etc.)
      let bloodGroup = updateData.bloodGroup;
      let rhType = updateData.rhType;

      // If bloodGroup is empty but bloodType is provided, parse it
      if ((!bloodGroup || bloodGroup.trim() === "") && updateData.bloodType) {
        const bloodTypeMatch = updateData.bloodType.match(/^(A|B|AB|O)([+-]?)$/);
        if (bloodTypeMatch) {
          bloodGroup = bloodTypeMatch[1];
          if (bloodTypeMatch[2]) {
            rhType = bloodTypeMatch[2] === "+" ? "Rh+" : "Rh-";
          }
        }
      }

      // Only include fields that have actual values
      if (bloodGroup && bloodGroup.trim() !== "") {
        // Validate blood group against known values
        const validBloodGroups = ["A", "B", "AB", "O"];
        const cleanBloodGroup = bloodGroup.trim();
        if (validBloodGroups.includes(cleanBloodGroup)) {
          payload.bloodGroup = cleanBloodGroup;
        } else {
          throw new Error(`Invalid blood group: ${cleanBloodGroup}. Must be one of: ${validBloodGroups.join(", ")}`);
        }
      }

      if (rhType && rhType.trim() !== "") {
        // Validate Rh type against known values
        const validRhTypes = ["Rh+", "Rh-"];
        const cleanRhType = rhType.trim();
        if (validRhTypes.includes(cleanRhType)) {
          payload.rhType = cleanRhType;
        } else {
          throw new Error(`Invalid Rh type: ${cleanRhType}. Must be one of: ${validRhTypes.join(", ")}`);
        }
      }

      if (updateData.donationDate && updateData.donationDate.trim() !== "") {
        // Format donation date properly - API expects full ISO datetime string
        try {
          const date = new Date(updateData.donationDate);
          if (isNaN(date.getTime())) {
            throw new Error("Invalid date");
          }
          // API expects full ISO datetime string like "2025-07-25T06:53:02.501Z"
          payload.donationDate = date.toISOString();
        } catch (dateError) {
          throw new Error(`Invalid donation date format: ${updateData.donationDate}`);
        }
      }

      // Validate that we have blood testing fields to update
      const hasBloodTestingFields = payload.bloodGroup || payload.rhType || payload.donationDate;
      if (!hasBloodTestingFields) {
        throw new Error("No blood testing fields provided for update. Need bloodGroup, rhType, or donationDate.");
      }

      // For blood testing, we should have both bloodGroup and rhType
      if (payload.bloodGroup && !payload.rhType) {
        throw new Error("Blood group provided but Rh type is missing. Both are required for blood testing.");
      }
      if (payload.rhType && !payload.bloodGroup) {
        throw new Error("Rh type provided but blood group is missing. Both are required for blood testing.");
      }

      // Debug: Log the payload being sent
      console.log('🩸 Blood Testing Update Payload:', JSON.stringify(payload, null, 2));
      console.log('🩸 Original updateData:', JSON.stringify(updateData, null, 2));

      // Prepare final payload according to API specification
      const finalPayload = {
        doctorId: payload.doctorId,
        process: 4 // Blood testing process (always 4 for this endpoint)
      };

      // Add blood testing fields if available
      if (payload.bloodGroup) {
        finalPayload.bloodGroup = payload.bloodGroup;
      }

      if (payload.rhType) {
        finalPayload.rhType = payload.rhType;
      }

      if (payload.donationDate) {
        finalPayload.donationDate = payload.donationDate;
      }

      console.log('🩸 Final payload being sent:', JSON.stringify(finalPayload, null, 2));

      // Use the correct API endpoint for examination (blood testing)
      const response = await apiClient.put(
        `/Appointment/doctor-examination/${validAppointmentId}`,
        finalPayload
      );

      return response.data;
    } catch (error) {
      // Enhanced error logging for debugging
      console.error('❌ Blood Testing Update Error:', {
        appointmentId: appointmentId,
        payload: finalPayload,
        error: error.response?.data || error.message,
        status: error.response?.status,
        statusText: error.response?.statusText
      });

      // Throw a more descriptive error
      const errorMessage = error.response?.data?.message || error.response?.data || error.message;
      throw new Error(`Blood testing update failed: ${errorMessage}`);
    }
  }

  // Legacy method for backward compatibility - now routes to appropriate stage method
  async doctorUpdateAppointment(appointmentId, updateData) {
    // Determine which stage based on the process or fields being updated
    const isBloodTestingStage = updateData.process === 4 ||
      updateData.bloodGroup ||
      updateData.rhType ||
      updateData.donationDate;

    if (isBloodTestingStage) {
      return this.doctorBloodTestingUpdate(appointmentId, updateData);
    } else {
      return this.doctorHealthExaminationUpdate(appointmentId, updateData);
    }
  }

  // Update user weight and height information with simplified approach
  async updateUserInformation(userId, updateData) {
    try {

      const validUserId = parseInt(userId);
      if (isNaN(validUserId) || validUserId <= 0) {
        throw new Error(`Invalid userId: ${userId}`);
      }

      // API only supports PUT method, so we'll use PUT with full user object

      // Fallback to PUT with full user object
      let currentInfo = {};
      try {
        currentInfo = await this.getUserInfo(validUserId);
      } catch (getError) {
        // If we can't get current info, try with minimal data
        currentInfo = {};
      }

      // Create full user object for PUT request
      const fullUserData = {
        userID: validUserId,
        email: currentInfo.email || "",
        password: "Ab1234@", // Use default password
        phone: currentInfo.phone || currentInfo.phoneNumber || "",
        idCardType: currentInfo.idCardType || "",
        idCard: currentInfo.idCard || "",
        name: currentInfo.name || currentInfo.fullName || "",
        dateOfBirth: currentInfo.dateOfBirth || null,
        age: currentInfo.age || null,
        gender: currentInfo.gender || "",
        city: currentInfo.city || "",
        district: currentInfo.district || "",
        ward: currentInfo.ward || "",
        address: currentInfo.address || "",
        distance: currentInfo.distance || null,
        bloodGroup: currentInfo.bloodGroup || currentInfo.bloodType || "",
        rhType: currentInfo.rhType || "",
        weight: updateData.weight !== undefined ? updateData.weight : (currentInfo.weight || currentInfo.Weight || null),
        height: updateData.height !== undefined ? updateData.height : (currentInfo.height || currentInfo.Height || null),
        status: currentInfo.status || 1,
        roleID: currentInfo.roleID || 1,
        department: currentInfo.department || ""
      };


      const response = await apiClient.put(
        `/Information/${validUserId}`,
        fullUserData
      );

      return response.data;

    } catch (error) {
      throw error;
    }
  }

  /**
   * Simple method to update only weight and height (backup method)
   * @param {number} userId - User ID
   * @param {Object} data - Weight and/or height data
   * @returns {Promise} API response
   */
  async updateWeightHeight(userId, data) {
    try {

      // Get current user info first
      const currentInfo = await this.getUserInfo(userId);

      // Create minimal payload with only essential fields
      const minimalPayload = {
        userID: parseInt(userId),
        email: currentInfo.email || "",
        password: "Ab1234@",
        name: currentInfo.name || currentInfo.fullName || "",
        weight: data.weight !== undefined ? parseFloat(data.weight) : (currentInfo.weight || null),
        height: data.height !== undefined ? parseFloat(data.height) : (currentInfo.height || null),
        status: currentInfo.status || 1,
        roleID: currentInfo.roleID || 1
      };

      const response = await apiClient.put(`/Information/${userId}`, minimalPayload);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async updateAppointmentProcess(appointmentId, process, notes = null, userId = null) {
    try {
      // Validate and convert parameters
      const validAppointmentId = parseInt(appointmentId);
      const validProcess = parseInt(process);

      // Validate parameters
      if (isNaN(validAppointmentId) || validAppointmentId <= 0) {
        throw new Error(`Invalid appointmentId: ${appointmentId}`);
      }
      if (isNaN(validProcess) || validProcess < 1 || validProcess > 5) {
        throw new Error(`Invalid process: ${process} (must be 1-5)`);
      }

      // API expects integer process parameter
      // Add headers to bypass notification logging completely
      const headers = {
        'X-Skip-Notification': 'true',
        'X-Disable-Notification': 'true',
        'Skip-Notification': 'true',
        'X-No-Notification': 'true',
        'No-Notification': 'true',
        'no-notification': 'true',
        'Disable-Logging': 'true',
        'disable-logging': 'true',
        'X-Bypass-Notification': 'true',
        'x-bypass-notification': 'true',
        'x-skip-notification': 'true',
        'x-disable-notification': 'true',
        'x-no-notification': 'true',
        'skip-notification': 'true'
      };

      // Add userId to headers if available to help backend identify correct user
      if (userId) {
        headers['X-User-ID'] = userId.toString();
        headers['User-ID'] = userId.toString();
        headers['user-id'] = userId.toString();
        headers['userId'] = userId.toString();
        headers['x-user-id'] = userId.toString();
      }

      // Try different ways to send notes
      const approaches = [
        // Approach 1: Notes in request body only
        async () => {
          const requestBody = notes ? { notes } : {};
          const url = `/Appointment/${validAppointmentId}/process/${validProcess}?skipNotification=true&noLog=true`;
          return await apiClient.patch(url, requestBody, { headers });
        },

        // Approach 2: Try "note" (singular) in request body
        async () => {
          const requestBody = notes ? { note: notes } : {};
          const url = `/Appointment/${validAppointmentId}/process/${validProcess}?skipNotification=true&noLog=true`;
          return await apiClient.patch(url, requestBody, { headers });
        },

        // Approach 3: Notes as query parameter only
        async () => {
          const url = `/Appointment/${validAppointmentId}/process/${validProcess}?skipNotification=true&noLog=true&notes=${encodeURIComponent(notes || '')}`;
          return await apiClient.patch(url, {}, { headers });
        },

        // Approach 4: Try "note" (singular) as query parameter
        async () => {
          const url = `/Appointment/${validAppointmentId}/process/${validProcess}?skipNotification=true&noLog=true&note=${encodeURIComponent(notes || '')}`;
          return await apiClient.patch(url, {}, { headers });
        }
      ];

      let lastError = null;
      for (let i = 0; i < approaches.length; i++) {
        try {
          const response = await approaches[i]();
          return response;
        } catch (error) {
          lastError = error;
          continue;
        }
      }

      // If all approaches failed, throw the last error
      throw lastError;
    } catch (error) {



      throw error;
    }
  }

  /**
   * Update appointment notes only
   * @param {number} appointmentId - Appointment ID
   * @param {string} notes - Notes to update
   * @returns {Promise} API response
   */
  async updateAppointmentNotes(appointmentId, notes) {
    try {
      const validAppointmentId = parseInt(appointmentId);
      if (isNaN(validAppointmentId) || validAppointmentId <= 0) {
        throw new Error(`Invalid appointmentId: ${appointmentId}`);
      }



      // Add headers to bypass notification logging
      const headers = {
        'Content-Type': 'application/json',
        'X-Skip-Notification': 'true',
        'X-Disable-Notification': 'true',
        'Skip-Notification': 'true',
        'X-No-Notification': 'true',
        'No-Notification': 'true',
        'no-notification': 'true',
        'Disable-Logging': 'true',
        'disable-logging': 'true',
        'X-Bypass-Notification': 'true',
        'x-bypass-notification': 'true',
        'x-skip-notification': 'true',
        'x-disable-notification': 'true',
        'x-no-notification': 'true',
        'skip-notification': 'true'
      };

      // Try different payload formats for the dedicated note endpoint
      const approaches = [
        // Approach 1: PATCH /Appointment/{id}/note with notes in body
        async () => {
          return await apiClient.patch(
            `/Appointment/${validAppointmentId}/note`,
            { notes: notes },
            { headers }
          );
        },

        // Approach 2: PATCH /Appointment/{id}/note with note (singular) in body
        async () => {
          return await apiClient.patch(
            `/Appointment/${validAppointmentId}/note`,
            { note: notes },
            { headers }
          );
        },

        // Approach 3: PATCH /Appointment/{id}/note with plain text
        async () => {
          return await apiClient.patch(
            `/Appointment/${validAppointmentId}/note`,
            notes,
            {
              headers: {
                ...headers,
                'Content-Type': 'text/plain'
              }
            }
          );
        },

        // Approach 4: PATCH /Appointment/{id}/note with query parameter
        async () => {
          return await apiClient.patch(
            `/Appointment/${validAppointmentId}/note?note=${encodeURIComponent(notes)}`,
            {},
            { headers }
          );
        }
      ];

      let lastError = null;
      for (let i = 0; i < approaches.length; i++) {
        try {
          const response = await approaches[i]();

          return response;
        } catch (error) {
          lastError = error;
          continue;
        }
      }

      // If all approaches failed, throw the last error
      throw lastError;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update appointment notes using the dedicated notes endpoint
   * @param {number} appointmentId - Appointment ID
   * @param {string} notes - Notes to update
   * @returns {Promise} API response
   */
  async updateAppointmentNotes(appointmentId, notes) {
    try {
      const validAppointmentId = parseInt(appointmentId);
      if (isNaN(validAppointmentId) || validAppointmentId <= 0) {
        throw new Error(`Invalid appointmentId: ${appointmentId}`);
      }

      const response = await apiClient.patch(
        `/Appointment/${validAppointmentId}/note`,
        { notes: notes || "" }
      );
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update appointment with doctor notes and health check data
   * @param {number} appointmentId - Appointment ID
   * @param {Object} updateData - Data to update (notes, health check data)
   * @returns {Promise} API response
   */
  async updateAppointmentDoctorData(appointmentId, updateData) {
    try {
      // Determine which stage based on the process or fields being updated
      const isBloodTestingStage = updateData.process === 4 ||
        updateData.bloodGroup ||
        updateData.rhType ||
        updateData.donationDate;

      if (isBloodTestingStage) {
        return this.doctorBloodTestingUpdate(appointmentId, updateData);
      } else {
        return this.doctorHealthExaminationUpdate(appointmentId, updateData);
      }
    } catch (error) {
      throw error;
    }
  }





  /**
   * Update blood donation history with blood test results
   * @param {number} id - Blood donation history ID
   * @param {Object} data - Blood test data to update
   * @returns {Promise} API response
   */
  async updateBloodDonationHistory(id, data) {
    try {

      // Prepare the payload according to API specification
      const payload = {
        appointmentId: data.appointmentId || 0,
        donationDate: data.donationDate || new Date().toISOString(),
        bloodGroup: data.bloodGroup || "",
        rhType: data.rhType || "",
        doctorId: data.doctorId || 0,
        notes: data.notes || "",
        isSuccess: data.isSuccess !== undefined ? data.isSuccess : true
      };

      const response = await apiClient.put(`/BloodDonationHistory/${id}`, payload);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get user information
   * @param {number} userId - User ID
   * @returns {Promise} API response
   */
  async getUserInformation(userId) {
    try {

      const response = await apiClient.get(`/Information/${userId}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get all appointments (for admin/manager)
   * @returns {Promise} API response
   */
  async getAllAppointments() {
    try {
      // Try different endpoints to get all appointments including cancelled ones
      const possibleEndpoints = [
        "/Appointment?includeCancelled=true",
        "/Appointment?includeAll=true",
        "/Appointment?status=all",
        "/Appointment?limit=100",
        "/Appointment",
      ];

      for (const endpoint of possibleEndpoints) {
        try {
          const response = await apiClient.get(endpoint);

          // Return the first successful response with the most data
          if (response.data && response.data.length >= 12) {
            return response.data;
          }
        } catch (endpointError) {
          continue;
        }
      }

      // Fallback to basic endpoint
      const response = await apiClient.get("/Appointment");
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Cancel appointment (soft delete using PATCH method)
   * @param {number} appointmentId - Appointment ID
   * @returns {Promise} API response
   */
  async cancelAppointment(appointmentId) {
    try {

      const response = await apiClient.patch(
        `/Appointment/${appointmentId}/cancel`
      );

      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Delete appointment (alternative method name for backward compatibility)
   * @param {number} appointmentId - Appointment ID
   * @returns {Promise} API response
   */
  async deleteAppointment(appointmentId) {
    return this.cancelAppointment(appointmentId);
  }
}

// Create singleton instance
const bloodDonationService = new BloodDonationService();

export default bloodDonationService;
