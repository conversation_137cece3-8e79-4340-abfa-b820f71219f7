import React from "react";
import {
  <PERSON><PERSON>, <PERSON><PERSON>, Card, Avatar, Row, Col, Descriptions, Badge,
  Divider, Select, Input, Tag
} from "antd";
import {
  EditOutlined, UserOutlined, CheckCircleOutlined,
  MedicineBoxOutlined, ExperimentOutlined, FileTextOutlined
} from "@ant-design/icons";
import { DOCTOR_WORKFLOW_STAGES } from "../../../constants/systemConstants";


const { TextArea } = Input;

/**
 * Modal cập nhật trạng thái hiến máu
 */
const DonorStatusModal = ({
  visible,
  onCancel,
  onSave,
  selectedDonor,
  statusUpdateData,
  setStatusUpdateData
}) => {

  const getTimeSlotText = (slot) => {
    if (slot === "morning" || slot === "Sáng (7:00-12:00)") {
      return "7:00 - 12:00";
    } else if (slot === "afternoon" || slot === "<PERSON>ều (13:00-17:00)") {
      return "13:00 - 17:00";
    }
    return slot || "N/A";
  };

  const getProcessStepText = (process) => {
    const steps = {
      1: "Đăng ký",
      2: "Khám sức khỏe cơ bản",
      3: "Lấy máu",
      4: "Xét nghiệm máu",
      5: "Nhập kho"
    };
    return steps[process] || "Không xác định";
  };

  // Determine current workflow stage based on process
  const getCurrentWorkflowStage = () => {
    const currentProcess = selectedDonor?.process || 2;

    if (currentProcess === DOCTOR_WORKFLOW_STAGES.BLOOD_TESTING.process) {
      return DOCTOR_WORKFLOW_STAGES.BLOOD_TESTING;
    } else {
      return DOCTOR_WORKFLOW_STAGES.HEALTH_EXAMINATION;
    }
  };

  const currentStage = getCurrentWorkflowStage();

  if (!selectedDonor) return null;

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <EditOutlined style={{ color: '#1890ff' }} />
          <span>Cập nhật trạng thái hiến máu - {currentStage.name}</span>
          <Badge
            color={currentStage.process === 2 ? "#fa8c16" : "#13c2c2"}
            text={`Bước ${currentStage.process}`}
          />
        </div>
      }
      open={visible}
      onCancel={onCancel}
      width={700}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          Hủy
        </Button>,
        <Button key="save" type="primary" onClick={onSave}>
          <CheckCircleOutlined /> Lưu cập nhật
        </Button>
      ]}
    >
      {/* Donor Summary */}
      <Card
        size="small"
        style={{ marginBottom: 16, backgroundColor: '#f8f9fa' }}
      >
        <Row gutter={16}>
          <Col span={8}>
            <div style={{ textAlign: 'center' }}>
              <Avatar size={64} icon={<UserOutlined />} style={{ backgroundColor: '#1890ff' }} />
              <div style={{ marginTop: 8, fontWeight: 'bold', color: '#1890ff' }}>
                {selectedDonor.name}
              </div>
            </div>
          </Col>
          <Col span={16}>
            <Descriptions column={1} size="small">
              <Descriptions.Item label="Nhóm máu">
                <Tag color="red" style={{ fontSize: '14px' }}>
                  {selectedDonor.bloodType}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Trạng thái hiện tại">
                <Badge
                  color={selectedDonor.status ? "#52c41a" : "#ff4d4f"}
                  text={selectedDonor.status ? "Chấp nhận" : "Không chấp nhận"}
                />
              </Descriptions.Item>
              <Descriptions.Item label="Quy trình hiện tại">
                {selectedDonor.process ? (
                  <Tag color="blue">
                    Bước {selectedDonor.process}: {getProcessStepText(selectedDonor.process)}
                  </Tag>
                ) : (
                  <Tag color="default">Chưa có quy trình</Tag>
                )}
              </Descriptions.Item>

              {selectedDonor.doctorId && (
                <Descriptions.Item label="Bác sĩ phụ trách">
                  ID: {selectedDonor.doctorId}
                </Descriptions.Item>
              )}
            </Descriptions>
          </Col>
        </Row>
      </Card>

      {/* Status Update Section */}
      <Divider orientation="left">🔄 Cập nhật trạng thái</Divider>
      <div style={{ marginBottom: 16 }}>
        <label style={{ display: 'block', marginBottom: 8, fontWeight: 'bold' }}>
          Chọn trạng thái:
        </label>
        <Select
          style={{ width: '100%' }}
          value={statusUpdateData.status}
          onChange={(value) =>
            setStatusUpdateData((prev) => ({
              ...prev,
              status: value,
            }))
          }
          placeholder="Chọn trạng thái"
        >
          <Select.Option value="1">
            <Badge color="#ff4d4f" text="Không chấp nhận" />
          </Select.Option>
          <Select.Option value="2">
            <Badge color="#52c41a" text="Chấp nhận" />
          </Select.Option>
        </Select>
      </div>


      <div style={{ marginBottom: 16 }}>


        <div style={{ marginTop: 8 }}>
          <label style={{ display: 'block', marginBottom: 8, fontWeight: 'bold' }}>
            Chọn bước quy trình để cập nhật:
          </label>
          <Select
            style={{ width: '100%' }}
            value={statusUpdateData.process}
            onChange={(value) =>
              setStatusUpdateData((prev) => ({
                ...prev,
                process: value,
              }))
            }
            placeholder="Chọn bước quy trình"
          >
            <Select.Option value="2">
              <Badge color="#fa8c16" text="Khám sức khỏe cơ bản" />
            </Select.Option>
            <Select.Option value="3">
              <Badge color="#722ed1" text="Lấy máu" />
            </Select.Option>
            <Select.Option value="4">
              <Badge color="#13c2c2" text="Xét nghiệm máu" />
            </Select.Option>

          </Select>
        </div>
      </div>

      {/* Notes Section */}

      <Divider orientation="left">
        <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <FileTextOutlined style={{ color: '#722ed1' }} />
          <span>Ghi chú - {currentStage.name}</span>
        </span>
      </Divider>
      <TextArea
        value={statusUpdateData.notes}
        onChange={(e) =>
          setStatusUpdateData((prev) => ({
            ...prev,
            notes: e.target.value,
          }))
        }
        placeholder={
          currentStage.process === 2
            ? "Nhập ghi chú về kết quả khám sức khỏe (huyết áp, nhịp tim, hemoglobin, nhiệt độ)..."
            : "Nhập ghi chú về kết quả xét nghiệm máu (nhóm máu, Rh type, ngày hiến máu thực tế)..."
        }
        rows={4}
        style={{ marginBottom: 16 }}
      />
    </Modal>
  );
};

export default DonorStatusModal;
