import axiosInstance from "./axiosInstance";

const PATIENT_API = import.meta.env.VITE_PATIENT_API;

const patientService = {
  // Get patient information by ID for auto-fill
  getPatientById: async (patientId) => {
    try {
      const response = await axiosInstance.get(`${PATIENT_API}/${patientId}`);

      return {
        success: true,
        data: response.data,
        message: "Lấy thông tin bệnh nhân thành công",
      };
    } catch (error) {
      return {
        success: false,
        error:
          error.response?.data?.message ||
          "Có lỗi xảy ra khi lấy thông tin bệnh nhân",
        details: error.response?.data,
      };
    }
  },

  // Search patients by name or ID for dropdown
  searchPatients: async (searchTerm) => {
    try {
      const response = await axiosInstance.get(`${PATIENT_API}/search`, {
        params: { q: searchTerm },
      });

      return {
        success: true,
        data: response.data,
        message: "<PERSON><PERSON><PERSON> kiếm bệnh nhân thành công",
      };
    } catch (error) {
      return {
        success: false,
        error:
          error.response?.data?.message ||
          "Có lỗi xảy ra khi tìm kiếm bệnh nhân",
        details: error.response?.data,
      };
    }
  },

  // Get all patients for doctor's department
  getPatientsByDoctor: async (doctorId) => {
    try {
      const response = await axiosInstance.get(
        `${PATIENT_API}/doctor/${doctorId}`
      );

      return {
        success: true,
        data: response.data,
        message: "Lấy danh sách bệnh nhân thành công",
      };
    } catch (error) {
      return {
        success: false,
        error:
          error.response?.data?.message ||
          "Có lỗi xảy ra khi lấy danh sách bệnh nhân",
        details: error.response?.data,
      };
    }
  },
};

export default patientService;
