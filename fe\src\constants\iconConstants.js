import React from 'react';
import {
  // Status icons
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  SyncOutlined,

  // Medical icons
  HeartOutlined,
  MedicineBoxOutlined,
  ExperimentOutlined,
  SafetyOutlined,

  // General icons
  FileTextOutlined,
  UserOutlined,
  CalendarOutlined,
  HomeOutlined,
  BellOutlined,
  AlertOutlined,
  <PERSON>boltOutlined,

  // Action icons
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  EyeOutlined,

  // System icons
  DatabaseOutlined,
  BarChartOutlined,
  SettingOutlined,
  QuestionCircleOutlined,
  InfoCircleOutlined,
  WarningOutlined,

  // Blood specific
  DropboxOutlined,
  TeamOutlined,

} from '@ant-design/icons';

import {
  // React Icons for more variety
  FaHospital,
  FaVial,
  FaUserMd,
  FaAmbulance,
  FaHeartbeat,
  FaFlask,
  FaWarehouse,
  FaClipboardCheck,
  FaUserCheck,
  FaBan,
} from 'react-icons/fa';

import {
  MdBloodtype,
  MdLocalHospital,
  MdScience,
  MdInventory,
  MdEmergency,
  MdHealthAndSafety,
} from 'react-icons/md';

/**
 * Unified Icon System
 * Thay thế tất cả emoji cứng bằng icon chuyên nghiệp
 */

// Blood & Medical Icon Functions
export const createBloodIcon = () => React.createElement(MdBloodtype, { style: { color: '#d32f2f' } });
export const createHeartIcon = () => React.createElement(HeartOutlined, { style: { color: '#d32f2f' } });
export const createHospitalIcon = () => React.createElement(FaHospital, { style: { color: '#1890ff' } });
export const createMedicalBoxIcon = () => React.createElement(MedicineBoxOutlined, { style: { color: '#52c41a' } });
export const createDoctorIcon = () => React.createElement(FaUserMd, { style: { color: '#722ed1' } });
export const createHealthCheckIcon = () => React.createElement(MdHealthAndSafety, { style: { color: '#13c2c2' } });

// Status Icon Functions
export const createSuccessIcon = () => React.createElement(CheckCircleOutlined, { style: { color: '#52c41a' } });
export const createPendingIcon = () => React.createElement(ClockCircleOutlined, { style: { color: '#faad14' } });
export const createWarningIcon = () => React.createElement(WarningOutlined, { style: { color: '#fa8c16' } });
export const createErrorIcon = () => React.createElement(CloseCircleOutlined, { style: { color: '#f5222d' } });
export const createInfoIcon = () => React.createElement(InfoCircleOutlined, { style: { color: '#1890ff' } });
export const createQuestionIcon = () => React.createElement(QuestionCircleOutlined, { style: { color: '#8c8c8c' } });

// Donation Status Icon Functions
export const createRegisteredIcon = () => React.createElement(FileTextOutlined, { style: { color: '#1890ff' } });
export const createHealthCheckedIcon = () => React.createElement(FaUserCheck, { style: { color: '#13c2c2' } });
export const createNotEligibleIcon = () => React.createElement(FaBan, { style: { color: '#f5222d' } });
export const createDonatedIcon = () => React.createElement(MdBloodtype, { style: { color: '#52c41a' } });
export const createBloodTestedIcon = () => React.createElement(FaFlask, { style: { color: '#faad14' } });
export const createCompletedIcon = () => React.createElement(CheckCircleOutlined, { style: { color: '#52c41a' } });
export const createStoredIcon = () => React.createElement(FaWarehouse, { style: { color: '#722ed1' } });

// Urgency Icon Functions
export const createNormalIcon = () => React.createElement(FileTextOutlined, { style: { color: '#52c41a' } });
export const createUrgentIcon = () => React.createElement(ThunderboltOutlined, { style: { color: '#fa8c16' } });
export const createCriticalIcon = () => React.createElement(MdEmergency, { style: { color: '#f5222d' } });

// Process Flow Icon Functions
export const createRegisterIcon = () => React.createElement(FileTextOutlined, { style: { color: '#1890ff' } });
export const createHealthCheckProcessIcon = () => React.createElement(MdHealthAndSafety, { style: { color: '#13c2c2' } });
export const createDonateIcon = () => React.createElement(MdBloodtype, { style: { color: '#52c41a' } });
export const createTestIcon = () => React.createElement(MdScience, { style: { color: '#faad14' } });
export const createCompleteIcon = () => React.createElement(CheckCircleOutlined, { style: { color: '#52c41a' } });
export const createStoreIcon = () => React.createElement(MdInventory, { style: { color: '#722ed1' } });

// Notification Icon Functions
export const createReminderIcon = () => React.createElement(CalendarOutlined, { style: { color: '#1890ff' } });
export const createAlertIcon = () => React.createElement(AlertOutlined, { style: { color: '#f5222d' } });
export const createReportIcon = () => React.createElement(FileTextOutlined, { style: { color: '#722ed1' } });
export const createBellIcon = () => React.createElement(BellOutlined, { style: { color: '#1890ff' } });

// Department Icon Functions
export const createBloodDepartmentIcon = () => React.createElement(MdBloodtype, { style: { color: '#d32f2f' } });
export const createGeneralDepartmentIcon = () => React.createElement(FaHospital, { style: { color: '#1890ff' } });

/**
 * Helper functions to get icons
 */
export const getDonationStatusIcon = (status) => {
  switch (status) {
    case 'REGISTERED': return createRegisteredIcon();
    case 'HEALTH_CHECKED': return createHealthCheckedIcon();
    case 'NOT_ELIGIBLE_HEALTH': return createNotEligibleIcon();
    case 'NOT_ELIGIBLE_TEST': return createNotEligibleIcon();
    case 'DONATED': return createDonatedIcon();
    case 'BLOOD_TESTED': return createBloodTestedIcon();
    case 'COMPLETED': return createCompletedIcon();
    case 'STORED': return createStoredIcon();
    case 'PENDING': return createPendingIcon();
    default: return createQuestionIcon();
  }
};

export const getUrgencyIcon = (level) => {
  switch (level) {
    case 'NORMAL': return createNormalIcon();
    case 'URGENT': return createUrgentIcon();
    case 'CRITICAL': return createCriticalIcon();
    default: return createNormalIcon();
  }
};

export const getNotificationIcon = (type) => {
  switch (type) {
    case 'Reminder':
    case 'appointment_reminder': return createReminderIcon();
    case 'Alert':
    case 'urgent_request': return createAlertIcon();
    case 'Report':
    case 'blood_request_update': return createReportIcon();
    case 'donation_reminder': return createBloodIcon();
    case 'health_check': return createHealthCheckIcon();
    case 'donation_thanks': return createHeartIcon();
    case 'system_announcement': return createBellIcon();
    case 'account_update': return React.createElement(UserOutlined, { style: { color: '#52c41a' } });
    default: return createBellIcon();
  }
};

export const getLogIcon = (type) => {
  switch (type) {
    case 'success': return createSuccessIcon();
    case 'error': return createErrorIcon();
    case 'warning': return createWarningIcon();
    case 'info': return createInfoIcon();
    default: return createInfoIcon();
  }
};

export const getDepartmentIcon = (isBloodDepartment) => {
  return isBloodDepartment ? createBloodDepartmentIcon() : createGeneralDepartmentIcon();
};

// Process Icons Object for backward compatibility
export const PROCESS_ICONS = {
  REGISTER: createRegisterIcon(),
  HEALTH_CHECK: createHealthCheckProcessIcon(),
  DONATE: createDonateIcon(),
  TEST: createTestIcon(),
  COMPLETE: createCompleteIcon(),
  STORE: createStoreIcon(),
};

// Blood Icons Object for backward compatibility
export const BLOOD_ICONS = {
  BLOOD_DROP: createBloodIcon(),
  HEART: createHeartIcon(),
  HOSPITAL: createHospitalIcon(),
  MEDICAL_BOX: createMedicalBoxIcon(),
  DOCTOR: createDoctorIcon(),
  HEALTH_CHECK: createHealthCheckIcon(),
};

export default {
  // Icon creation functions
  createBloodIcon,
  createHeartIcon,
  createHospitalIcon,
  createMedicalBoxIcon,
  createDoctorIcon,
  createHealthCheckIcon,
  createSuccessIcon,
  createPendingIcon,
  createWarningIcon,
  createErrorIcon,
  createInfoIcon,
  createQuestionIcon,
  createRegisteredIcon,
  createHealthCheckedIcon,
  createNotEligibleIcon,
  createDonatedIcon,
  createBloodTestedIcon,
  createCompletedIcon,
  createStoredIcon,
  createNormalIcon,
  createUrgentIcon,
  createCriticalIcon,
  createRegisterIcon,
  createHealthCheckProcessIcon,
  createDonateIcon,
  createTestIcon,
  createCompleteIcon,
  createStoreIcon,
  createReminderIcon,
  createAlertIcon,
  createReportIcon,
  createBellIcon,
  createBloodDepartmentIcon,
  createGeneralDepartmentIcon,

  // Helper functions
  getDonationStatusIcon,
  getUrgencyIcon,
  getNotificationIcon,
  getLogIcon,
  getDepartmentIcon,

  // Objects for backward compatibility
  PROCESS_ICONS,
  BLOOD_ICONS,
};
