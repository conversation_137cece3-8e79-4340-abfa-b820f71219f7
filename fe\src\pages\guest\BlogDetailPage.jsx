import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { fetchNewsById } from "../../services/newsService";
import userInfoService from "../../services/userInfoService";
import { Card, Spin, Typography, Button, Divider, Space } from "antd";
import {
  ArrowLeftOutlined,
  ClockCircleOutlined,
  UserOutlined,
  BookOutlined,
  TagOutlined,
} from "@ant-design/icons";
import useRequest from "../../hooks/useFetchData";
import ArticleTags from "../../components/common/ArticleTags";
import "../../styles/pages/BloodInfoPage.scss";

const { Title, Paragraph, Text } = Typography;

const BlogDetailPage = () => {
  const { postId } = useParams();
  const navigate = useNavigate();
  const [userMap, setUserMap] = useState({});

  // Check if this is a member route
  const isMember = window.location.pathname.includes("/member/");

  const { data: article, loading } = useRequest(
    () => fetchNewsById(postId),
    [postId],
    { forceArray: false } // Don't force to array for single object
  );

  // Fetch users to get author name
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const usersData = await getUsers();
        const tempUserMap = {};

        if (Array.isArray(usersData)) {
          usersData.forEach((user) => {
            const userId = user.id || user.userId || user.userID;
            const userName =
              user.name ||
              user.fullName ||
              user.username ||
              user.email ||
              `User ${userId}`;
            tempUserMap[userId] = userName;
          });
        }

        setUserMap(tempUserMap);
        console.log("User map:", tempUserMap);
      } catch (error) {
        console.error("Error fetching users:", error);
        setUserMap({});
      }
    };

    fetchUsers();
  }, []);



  if (loading) {
    return (
      <div className="article-detail-page">
        <div className="loading-container">
          <Spin size="large" tip="Đang tải bài viết..." />
        </div>
      </div>
    );
  }

  if (!article) {
    return (
      <div className="article-detail-page">
        <div className="error-container">
          <Title level={3}>
            <BookOutlined className="title-icon" /> Không tìm thấy bài viết
          </Title>
          <Paragraph>Bài viết không tồn tại hoặc đã bị xóa.</Paragraph>
          <Button
            type="primary"
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate(isMember ? "/member/blog" : "/blog")}
          >
            Quay lại danh sách
          </Button>
        </div>
      </div>
    );
  }

  // Format content with proper HTML rendering
  const formatContent = (content) => {
    console.log("Formatting blog content:", content);

    if (!content) {
      console.log("No blog content found");
      return <Paragraph>Nội dung bài viết không có sẵn.</Paragraph>;
    }

    // Handle different content formats
    if (typeof content === "string") {
      // If content contains HTML tags, render as HTML
      if (content.includes("<") && content.includes(">")) {
        return (
          <div
            className="content-html"
            dangerouslySetInnerHTML={{ __html: content }}
          />
        );
      }
      // Otherwise, split by line breaks and render as paragraphs
      return content.split("\n").map((paragraph, index) => (
        <Paragraph key={index} className="content-paragraph">
          {paragraph}
        </Paragraph>
      ));
    }

    // If content is an object or other format
    return <Paragraph>{String(content)}</Paragraph>;
  };

  return (
    <div className="article-detail-page">
      <div className="article-container">
        <Button
          type="link"
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate(isMember ? "/member/blog" : "/blog")}
          className="back-button"
        >
          Quay lại danh sách
        </Button>

        <Card className="article-card">
          <div className="article-header">
            <Title level={2} className="article-title">
              {article.title}
            </Title>

            {Array.isArray(article.tags) && article.tags.length > 0 && (
              <div className="article-tags">
                <TagOutlined className="tag-icon" />
                <ArticleTags tags={article.tags} />
              </div>
            )}
          </div>

          <div className="article-meta">
            <Space split={<Divider type="vertical" />}>
              <Space>
                <UserOutlined />
                <Text>
                  {userMap[article.userId] ||
                    userMap[article.userID] ||
                    article.userName ||
                    article.authorName ||
                    "Hệ thống"}
                </Text>
              </Space>
              <Space>
                <ClockCircleOutlined />
                <Text>
                  {new Date(
                    article.postedAt || article.createdAt || article.date
                  ).toLocaleDateString("vi-VN", {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                  })}
                </Text>
              </Space>
            </Space>
          </div>

          {article.image || article.imgUrl ? (
            <div className="article-image-wrapper">
              <img
                src={article.image || article.imgUrl}
                alt={article.title}
                className="article-image"
              />
            </div>
          ) : null}

          <div className="article-content">
            {formatContent(
              article.content ||
                article.description ||
                article.summary ||
                article.body ||
                "Nội dung bài viết không có sẵn."
            )}
          </div>
        </Card>
      </div>
    </div>
  );
};

export default BlogDetailPage;
