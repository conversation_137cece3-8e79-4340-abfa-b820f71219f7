import React, { useState, useEffect } from "react";
import {
  Form,
  Input,
  But<PERSON>,
  Card,
  Row,
  Col,
  Spin,
  Divider,
  Typography
} from "antd";

import {
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  IdcardOutlined,
  EnvironmentOutlined,
  ManOutlined,
  WomanOutlined,
  CalendarOutlined,
  HeartOutlined
} from "@ant-design/icons";
import { updateUserInfoById } from "../../services/informationService";
import authService from "../../services/authService";
import { updateStoredUserInfo } from "../../utils/userUtils";
import { toast } from "../../utils/toastUtils";

const { Title } = Typography;


/**
 * Component chung cho cập nhật hồ sơ cá nhân
 * Sử dụng cho tất cả các actor: Doctor, Manager, Admin
 * Chỉ hiển thị form cập nhật thông tin, không có đổi mật khẩu
 */
const ProfileUpdateForm = ({
  userInfo,
  onUpdateSuccess,
  title = "Cập nhật hồ sơ cá nhân"
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (userInfo) {
      console.log("ProfileUpdateForm - Received userInfo:", userInfo);
      console.log("ProfileUpdateForm - Available fields:", Object.keys(userInfo));
      console.log("ProfileUpdateForm - idCard:", userInfo.idCard);
      console.log("ProfileUpdateForm - bloodGroup:", userInfo.bloodGroup);
      console.log("ProfileUpdateForm - dateOfBirth:", userInfo.dateOfBirth);
      console.log("ProfileUpdateForm - gender:", userInfo.gender);
      console.log("ProfileUpdateForm - city/district/ward:", userInfo.city, userInfo.district, userInfo.ward);

      // Format date from API format to display format (similar to MemberInfoPage)
      const formatDateForDisplay = (dateString) => {
        if (!dateString) return "";
        try {
          return new Date(dateString).toLocaleDateString('vi-VN');
        } catch (error) {
          console.error("Error formatting date:", error);
          return "";
        }
      };

      // Format idCardType for display
      const formatIdCardType = (type) => {
        const typeMap = {
          'cccd': 'Căn cước công dân',
          'cmnd': 'Chứng minh nhân dân',
          'passport': 'Hộ chiếu'
        };
        return typeMap[type] || type || "";
      };

      // Map API fields to form fields based on MemberInfoPage mapping
      const mappedData = {
        // Editable fields (same as MemberInfoPage)
        fullName: userInfo.name || null,
        email: userInfo.email || null,
        phoneNumber: userInfo.phone || null,
        address: userInfo.address || null,

        // Read-only fields for display (same field names as API)
        idCard: userInfo.idCard || null,
        bloodGroup: userInfo.bloodGroup || null,

        // Formatted display fields
        locationInfo: `${userInfo?.ward || ''} ${userInfo?.district || ''} ${userInfo?.city || ''}`.trim(),
        dateOfBirthDisplay: formatDateForDisplay(userInfo.dateOfBirth),
        genderDisplay: userInfo.gender === 'male' ? 'Nam' : userInfo.gender === 'female' ? 'Nữ' : userInfo.gender === 'other' ? 'Khác' : userInfo.gender || "",
        ageDisplay: userInfo.age ? `${userInfo.age} tuổi` : "",

        // Keep original values for reference and form submission
        idCardType: formatIdCardType(userInfo.idCardType),
        city: userInfo.city || null,
        district: userInfo.district || null,
        ward: userInfo.ward || null,
        rhType: userInfo.rhType || null,
        weight: userInfo.weight || null,
        height: userInfo.height || null,
        gender: userInfo.gender || null,
        dateOfBirth: userInfo.dateOfBirth || "",
      };

      console.log("ProfileUpdateForm - Mapped data:", mappedData);
      form.setFieldsValue(mappedData);
    }
  }, [userInfo, form]);

  const handleUpdateProfile = async (values) => {
    try {
      setLoading(true);
      console.log("ProfileUpdateForm - Submitting values:", values);
      console.log("ProfileUpdateForm - Current userInfo:", userInfo);

      // Get current user ID
      const currentUser = authService.getCurrentUser();
      let userId = currentUser?.id;

      // Also try to get userId from JWT token as fallback
      const token = localStorage.getItem("authToken");
      if (token && (!userId || userId === "0" || userId === 0)) {
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          const jwtUserId = payload["http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier"];
          if (jwtUserId && jwtUserId !== "0") {
            userId = jwtUserId;
          }
        } catch (e) {
          console.error("Error parsing JWT:", e);
        }
      }

      if (!userId) {
        toast.error("Không thể xác định người dùng hiện tại!");
        return;
      }

      // Map form values to API format - only update editable fields, keep all others unchanged
      const updateData = {
        // Required fields
        userID: parseInt(userId),

        // Editable fields - only these can be changed (email is not editable)
        email: userInfo?.email || "", // Keep original email, don't allow changes
        phone: values.phoneNumber || userInfo?.phone || null,
        name: values.fullName || userInfo?.name || null,
        address: values.address || userInfo?.address || null,

        // Keep all existing non-editable fields exactly as they are
        password: userInfo?.password || "Ab1234@",
        idCardType: userInfo?.idCardType || null,
        idCard: userInfo?.idCard || null,
        dateOfBirth: userInfo?.dateOfBirth ? new Date(userInfo.dateOfBirth).toISOString() : null,
        age: userInfo?.age || null,
        gender: userInfo?.gender || null,
        city: userInfo?.city || null,
        district: userInfo?.district || null,
        ward: userInfo?.ward || null,
        distance: userInfo?.distance || null,
        bloodGroup: userInfo?.bloodGroup || null,
        rhType: userInfo?.rhType || null,
        weight: userInfo?.weight || null,
        height: userInfo?.height || null,
        status: userInfo?.status !== undefined ? userInfo.status : 1,
        roleID: userInfo?.roleID || 1,
        department: userInfo?.department || null,
        departmentId: userInfo?.departmentId || userInfo?.departmentID || null, // Support both naming conventions
        createdAt: userInfo?.createdAt || new Date().toISOString(),
      };

      // Remove null/undefined values that might cause API issues
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === null || updateData[key] === undefined) {
          if (key === 'dateOfBirth' || key === 'age' || key === 'distance' || key === 'weight' || key === 'height' || key === 'departmentId') {
            // Keep null for these fields as they might be expected
            // departmentId can be null for Manager role
          } else {
            updateData[key] = "";
          }
        }
      });

      // Special handling for Manager role - ensure departmentId is null
      if (currentUser?.role === 'Manager' || userInfo?.roleID === 3) {
        console.log("Detected Manager role - setting departmentId to null");
        updateData.departmentId = null;
        updateData.department = "";
      }

      console.log("Final updateData before API call:", updateData);
      console.log("User role check:", {
        currentUserRole: currentUser?.role,
        userInfoRoleID: userInfo?.roleID,
        isManager: currentUser?.role === 'Manager' || userInfo?.roleID === 3
      });

      console.log("ProfileUpdateForm - Update data:", updateData);
      console.log("ProfileUpdateForm - User ID:", userId);
      console.log("ProfileUpdateForm - Original userInfo:", userInfo);
      console.log("ProfileUpdateForm - departmentId check:", {
        original: userInfo?.departmentId,
        originalID: userInfo?.departmentID,
        final: updateData.departmentId
      });

      const response = await updateUserInfoById(userId, updateData);
      console.log("ProfileUpdateForm - API Response:", response);
      console.log("ProfileUpdateForm - Update data sent:", updateData);

      // Update localStorage with new user info for immediate UI update
      const updatedUserInfo = { ...userInfo, ...updateData };
      updateStoredUserInfo(updatedUserInfo);

      // Update auth service profile if available
      const authUser = authService.getCurrentUser();
      if (authUser && authUser.profile) {
        const updatedProfile = {
          ...authUser.profile,
          name: values.fullName,
          fullName: values.fullName,
          // email: values.email, // Don't update email
          phone: values.phoneNumber,
          address: values.address
        };
        authService.updateProfile(updatedProfile);
      }

      // Show success toast
      toast.success("Cập nhật thông tin thành công!");

      if (onUpdateSuccess) {
        onUpdateSuccess(updatedUserInfo);
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error("Cập nhật thông tin thất bại. Vui lòng thử lại!");
    } finally {
      setLoading(false);
    }
  };



  if (!userInfo) {
    return (
      <div style={{ textAlign: "center", padding: "50px" }}>
        <Spin size="large" />
        <p style={{ marginTop: "16px" }}>Đang tải thông tin...</p>
      </div>
    );
  }

  return (
    <div className="profile-update-container">
      <Title level={2} style={{ marginBottom: "24px", textAlign: "center" }}>
        {title}
      </Title>

      <Row justify="center">
        {/* Cập nhật thông tin cá nhân */}
        <Col xs={24} lg={16} xl={14}>
          <Card
            title={
              <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <UserOutlined />
                <span>Thông tin cá nhân</span>
              </div>
            }
          >
            
            <Form
              form={form}
              layout="vertical"
              onFinish={handleUpdateProfile}
              autoComplete="off"
            >
              <Form.Item
                label="Họ và tên"
                name="fullName"
                rules={[
                  { required: true, message: "Vui lòng nhập họ và tên!" },
                  { min: 2, message: "Họ và tên phải có ít nhất 2 ký tự!" },
                ]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="Nhập họ và tên"
                  size="large"
                />
              </Form.Item>

              <Form.Item
                label="Email"
                name="email"
                rules={[
                  { required: true, message: "Vui lòng nhập email!" },
                  { type: "email", message: "Email không hợp lệ!" },
                ]}
              >
                <Input
                  prefix={<MailOutlined />}
                  placeholder="Email không thể thay đổi"
                  size="large"
                  disabled
                  style={{
                    backgroundColor: '#f5f5f5',
                    color: '#666',
                    cursor: 'not-allowed'
                  }}
                />
              </Form.Item>

              <Form.Item
                label="Số điện thoại"
                name="phoneNumber"
                rules={[
                  { required: true, message: "Vui lòng nhập số điện thoại!" },
                  { 
                    pattern: /^[0-9]{10}$/, 
                    message: "Số điện thoại phải có 10 chữ số!" 
                  },
                ]}
              >
                <Input
                  prefix={<PhoneOutlined />}
                  placeholder="Nhập số điện thoại"
                  size="large"
                />
              </Form.Item>

              <Form.Item
                label="Địa chỉ"
                name="address"
                rules={[
                  { required: true, message: "Vui lòng nhập địa chỉ!" },
                ]}
              >
                <Input.TextArea
                  placeholder="Nhập địa chỉ"
                  rows={3}
                  size="large"
                />
              </Form.Item>

             
              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  size="large"
                  block
                  style={{ 
                    background: "#1890ff",
                    borderColor: "#1890ff",
                    height: "48px",
                    fontSize: "16px",
                    fontWeight: "500"
                  }}
                >
                  Cập nhật thông tin
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default ProfileUpdateForm;
