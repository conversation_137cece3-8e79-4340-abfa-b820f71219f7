import { useState } from "react";
import { Button } from "antd";
import { CalendarOutlined, ReloadOutlined } from "@ant-design/icons";
import { toast } from "../../utils/toastUtils";
import ManagerLayout from "../../components/manager/ManagerLayout";
import PageHeader from "../../components/manager/PageHeader";
import DonationScheduleTabs from "../../components/manager/donation-schedule/DonationScheduleTabs";
import DonationStatistics from "../../components/manager/donation-schedule/DonationStatistics";
import DonationDetailModal from "../../components/manager/donation-schedule/DonationDetailModal";
import ProcessWorkflowModal from "../../components/shared/ProcessWorkflowModal";

import bloodDonationService from "../../services/bloodDonationService";
import useDonationSchedule from "../../hooks/useDonationSchedule";
import {
  getScheduleDonations,
  getProcessDonations
} from "../../utils/donationScheduleHelpers";

import "../../styles/pages/manager/DonationSchedulePage.scss";
import "../../styles/components/PageHeader.scss";

const DonationSchedulePage = () => {
  const {
    // Data
    allDonations,
    loading,
    selectedDonation,
    processModalVisible,
    detailModalVisible,
    activeTab,
    scheduleSort,
    processSort,
    filters,
    isManager,

    // Actions
    setSelectedDonation,
    setProcessModalVisible,
    setDetailModalVisible,
    setActiveTab,
    setScheduleSort,
    setProcessSort,
    setFilters,
    refreshData,
    handleStoreBlood,
    handleSendReminder,
  } = useDonationSchedule();





  // Handle updating appointment process to "Nhập kho" (process = 5)
  const handleStoreBloodOverride = async (donationId) => {
    try {
      // Update appointment process to 5 (STORED) using PATCH /api/Appointment/{id}/process/{process}
      await bloodDonationService.updateAppointmentProcess(donationId, 5, "Đã nhập kho máu");
      toast.success("✅ Cập nhật quy trình thành công! Máu đã được nhập kho.");

      // Refresh data after successful update
      refreshData();
    } catch (error) {
      console.error("Error updating appointment process:", error);
      toast.error("❌ Cập nhật quy trình thất bại!");
    }
  };

  // Handle modal actions
  const handleViewDetails = (donation) => {
    setSelectedDonation(donation);
    setDetailModalVisible(true);
  };

  const handleViewWorkflow = (donation) => {
    setSelectedDonation(donation);
    setProcessModalVisible(true);
  };

  const closeDetailModal = () => {
    setDetailModalVisible(false);
    setSelectedDonation(null);
  };

  const closeWorkflowModal = () => {
    setProcessModalVisible(false);
    setSelectedDonation(null);
  };

  // Get filtered data for tabs
  const scheduleData = {
    donations: getScheduleDonations(allDonations, filters, scheduleSort),
    filters,
    onFilterChange: setFilters,
    scheduleSort,
    onSortChange: setScheduleSort,
  };

  const processData = {
    donations: getProcessDonations(allDonations, filters, processSort),
    filters,
    onFilterChange: setFilters,
    processSort,
    onSortChange: setProcessSort,
  };

  return (
    <ManagerLayout pageTitle="Quản lý lịch hiến máu">
      <div className="main-content">
        <PageHeader
          title="Quản lý lịch hiến máu"
          icon={CalendarOutlined}
          extra={
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={refreshData}
              loading={loading}
            >
              Làm mới
            </Button>
          }
        />

        {/* Statistics Cards */}
        <DonationStatistics donations={allDonations} />

        <DonationScheduleTabs
          activeTab={activeTab}
          onTabChange={setActiveTab}
          scheduleData={scheduleData}
          processData={processData}
          loading={loading}
          onViewDetails={handleViewDetails}
          onViewWorkflow={handleViewWorkflow}
          onSendReminder={handleSendReminder}
          onStoreBlood={handleStoreBloodOverride}
          isManager={isManager}
        />

        {/* Process Workflow Modal */}
        <ProcessWorkflowModal
          visible={processModalVisible}
          onCancel={closeWorkflowModal}
          selectedItem={selectedDonation}
          onStoreBlood={handleStoreBloodOverride}
          isManager={isManager}
          title="Quy trình hiến máu"
          onRefresh={refreshData}
        />

        {/* Detail Modal */}
        <DonationDetailModal
          visible={detailModalVisible}
          onCancel={closeDetailModal}
          donation={selectedDonation}
          onSendReminder={handleSendReminder}
        />


      </div>
    </ManagerLayout>
  );
};

export default DonationSchedulePage;