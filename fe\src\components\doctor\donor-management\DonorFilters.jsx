import React from "react";
import { Select, Button } from "antd";

/**
 * Component bộ lọc người hiến máu
 */
const DonorFilters = ({ filter, setFilter, statistics, loading, onRefresh }) => {
  const {
    todayCount,
    pendingCount,
    approvedCount,
    rejectedCount,
    cancelledCount
  } = statistics;

  return (
    <div style={{ marginBottom: 16 }}>
      <span style={{ marginRight: 8 }}>Lọc theo:</span>
      <Select
        value={filter}
        onChange={setFilter}
        style={{ width: 250 }}
        options={[
          { value: "all", label: "Tất cả" },
          { value: "today", label: `<PERSON>ô<PERSON> nay (${todayCount})` },
          { value: "pending", label: `<PERSON><PERSON> duyệt (${pendingCount})` },
          { value: "approved", label: `<PERSON><PERSON><PERSON> nhận (${approvedCount})` },
          { value: "rejected", label: `<PERSON><PERSON><PERSON><PERSON> chấp nhận (${rejectedCount})` },
          { value: "cancelled", label: `<PERSON><PERSON> hủy (${cancelledCount})` },
        ]}
      />
      <Button
        style={{ marginLeft: 16 }}
        onClick={onRefresh}
        loading={loading}
        type="primary"
      >
        Làm mới
      </Button>
    </div>
  );
};

export default DonorFilters;
