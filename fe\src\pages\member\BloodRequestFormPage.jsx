import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Container,
  Row,
  Col,
  Card,
  Form,
  <PERSON>ton,
  ProgressBar,
  <PERSON><PERSON>,
  Badge,
  Spinner,
} from "react-bootstrap";
import {
  <PERSON><PERSON><PERSON><PERSON>t,
  Fa<PERSON>ser,
  FaStethoscope,
  FaFileAlt,
  FaCheckCircle,
  FaArrowLeft,
  FaArrowRight,
  FaSpinner,
  FaHome,
  FaExclamationTriangle,
  FaRedo,
} from "react-icons/fa";
import MemberNavbar from "../../components/member/MemberNavbar";

import authService from "../../services/authService";
import userInfoService from "../../services/userInfoService";
import bloodRequestNotificationService from "../../services/bloodRequestNotificationService";
import bloodRequestService from "../../services/bloodRequestService";
import {
  formatBloodRequestData,
  formatBloodRequestResponse,
} from "../../utils/bloodRequestHelpers";
import { REQUEST_STATUS, BLOOD_TYPES } from "../../constants/systemConstants";
import { BLOOD_COMPONENT_MAP } from "../../constants/bloodInventoryConstants";
import UnifiedModal from "../../components/member/UnifiedModal";
import { uploadPdfMedicalReport } from "../../services/uploadService";
import ProfileIncompleteModal from "../../components/member/ProfileIncompleteModal";
import BloodRequestResultDisplay from "../../components/member/BloodRequest/BloodRequestResultDisplay";
import "../../styles/pages/BloodRequestFormPage.scss";
import "../../styles/components/BloodRequestResultDisplay.scss";

const BloodRequestFormPage = () => {
  // State cho upload PDF
  const [uploadingPdf, setUploadingPdf] = useState(false);
  const [pdfUploadError, setPdfUploadError] = useState("");
  const [selectedPdfFile, setSelectedPdfFile] = useState(null); // Lưu file PDF để gửi binary data
  const navigate = useNavigate();
  const currentUser = authService.getCurrentUser();

  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [submissionResult, setSubmissionResult] = useState(null);
  const [formData, setFormData] = useState({
    bloodType: "",
    componentId: "",
    quantity: "",
    unit: "ml",
    patientName: "",
    patientAge: "",
    patientGender: "",
    patientRelation: "",
    patientRelationOther: "",
    medicalCondition: "",
    hospitalName: "",
    doctorName: "",
    doctorPhone: "",
    medicalReports: "",
    additionalNotes: "",
  });
  const [validated, setValidated] = useState(false);
  const [attemptedSubmit, setAttemptedSubmit] = useState(false);
  const [touchedFields, setTouchedFields] = useState({});

  // Pending request check states
  const [isCheckingPending, setIsCheckingPending] = useState(true);
  const [pendingRequest, setPendingRequest] = useState(null);
  const [canCreateRequest, setCanCreateRequest] = useState(false);

  // Profile completeness check states
  const [isCheckingProfile, setIsCheckingProfile] = useState(true);
  const [profileCheckResult, setProfileCheckResult] = useState(null);
  const [profileComplete, setProfileComplete] = useState(false);

  // Modal states
  const [showModal, setShowModal] = useState(false);
  const [showProfileIncompleteModal, setShowProfileIncompleteModal] =
    useState(false);
  const [modalType, setModalType] = useState("profile-incomplete");
  const [modalContext, setModalContext] = useState("request");

  // Reset validation when step changes
  useEffect(() => {
    setValidated(false);
    setAttemptedSubmit(false);
    setTouchedFields({}); // Reset touched fields when step changes
  }, [currentStep]);

  // Check profile completeness when component mounts
  useEffect(() => {
    const checkProfileCompleteness = async () => {
      if (!currentUser?.id) {
        console.error("No current user found");
        setIsCheckingProfile(false);
        return;
      }

      try {
        setIsCheckingProfile(true);

        const userInfo = await userInfoService.getUserInfo(currentUser.id);
        const profileCheck = userInfoService.checkProfileCompleteness(userInfo);

        if (profileCheck.isComplete) {
          setProfileComplete(true);
        } else {
          // Profile incomplete - show modal and block form
          setProfileCheckResult(profileCheck);
          setProfileComplete(false);
          setShowProfileIncompleteModal(true);
        }
      } catch (error) {
        console.error("Error checking profile completeness:", error);
        // If there's an error, allow form access but log error
        setProfileComplete(true);
      } finally {
        setIsCheckingProfile(false);
      }
    };

    checkProfileCompleteness();
  }, [currentUser]);

  // Check for pending requests when component mounts (only if profile is complete)
  useEffect(() => {
    if (!profileComplete) return;

    const checkPendingRequests = async () => {
      if (!currentUser?.id) {
        console.error("No current user found");
        setIsCheckingPending(false);
        return;
      }

      try {
        setIsCheckingPending(true);

        const result = await bloodRequestService.checkUserPendingRequest(
          currentUser.id
        );

        if (result.success) {
          if (result.hasPendingRequest) {
            // User has pending request - show modal and block form
            setPendingRequest(result.pendingRequest);
            setCanCreateRequest(false);
            setModalType("blood-request-status");
            setShowModal(true);
          } else {
            // No pending request - allow form creation
            setCanCreateRequest(true);
          }
        } else {
          // Error checking - allow form creation but log error
          console.error("Error checking pending requests:", result.error);
          setCanCreateRequest(true);
        }
      } catch (error) {
        console.error("Exception checking pending requests:", error);
        setCanCreateRequest(true);
      } finally {
        setIsCheckingPending(false);
      }
    };

    checkPendingRequests();
  }, [currentUser?.id, profileComplete]);

  const steps = [
    {
      title: "Thông tin máu",
      icon: FaHeart,
      description: "Nhóm máu và số lượng cần thiết",
    },
    {
      title: "Thông tin bệnh nhân",
      icon: FaUser,
      description: "Chi tiết về bệnh nhân",
    },
    {
      title: "Thông tin y tế",
      icon: FaStethoscope,
      description: "Bệnh viện và bác sĩ điều trị",
    },
  ];

  const handleInputChange = (field, value) => {
    // Mark field as touched
    setTouchedFields((prev) => ({
      ...prev,
      [field]: true,
    }));

    // Special handling for doctor phone - only allow numbers
    if (field === "doctorPhone") {
      const numbersOnly = value.replace(/\D/g, "");
      setFormData((prev) => ({
        ...prev,
        [field]: numbersOnly,
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  // Helper function to determine if field should show validation error
  const shouldShowFieldError = (fieldName, condition) => {
    const shouldShow = validated || touchedFields[fieldName] || attemptedSubmit;
    return shouldShow && condition;
  };

  // Helper function to get field validation class
  const getFieldValidationClass = (fieldName, condition) => {
    return shouldShowFieldError(fieldName, condition) ? "is-invalid" : "";
  };

  const validateCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return formData.bloodType && formData.componentId && formData.quantity;
      case 1:
        const hasPatientRelation = formData.patientRelation;
        const hasPatientRelationOther =
          formData.patientRelation === "other"
            ? formData.patientRelationOther.trim()
            : true;
        const hasValidAge = formData.patientAge && formData.patientAge > 0;
        return (
          formData.patientName &&
          hasValidAge &&
          formData.patientGender &&
          formData.medicalCondition &&
          hasPatientRelation &&
          hasPatientRelationOther
        );
      case 2:
        const hasValidPhone =
          formData.doctorPhone && formData.doctorPhone.length === 10;
        return (
          formData.hospitalName &&
          formData.doctorName &&
          hasValidPhone &&
          formData.medicalReports
        );
      default:
        return false;
    }
  };

  const handleNext = () => {
    setAttemptedSubmit(true); // Mark that user attempted to proceed

    if (validateCurrentStep()) {
      // Reset all validation state immediately when moving to next step
      setValidated(false);
      setAttemptedSubmit(false);
      setTouchedFields({}); // Reset touched fields immediately

      // Use setTimeout to ensure state updates are applied before step change
      setTimeout(() => {
        setCurrentStep((prev) => prev + 1);
      }, 0);
    } else {
      setValidated(true);
    }
  };

  const handlePrev = () => {
    setCurrentStep((prev) => prev - 1);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    setAttemptedSubmit(true); // Mark that user attempted to submit
    if (!validateCurrentStep()) {
      setValidated(true);
      return;
    }

    setLoading(true);

    try {
      // Prepare blood request data for API using helper function
      const bloodRequestData = formatBloodRequestData(formData, currentUser);

      // Call API with FormData if we have PDF file, otherwise use JSON
      let apiResponse;
      if (selectedPdfFile) {
        // Use FormData approach with file binary data
        apiResponse = await bloodRequestService.createBloodRequestWithFile(
          bloodRequestData,
          selectedPdfFile
        );
      } else {
        // Use JSON approach with URL only
        apiResponse = await bloodRequestService.createBloodRequest(
          bloodRequestData
        );
      }

      if (!apiResponse.success) {
        // More detailed error message based on status
        let errorMessage =
          apiResponse.error || "Có lỗi xảy ra khi tạo yêu cầu máu";

        if (apiResponse.status === 400) {
          errorMessage =
            "Dữ liệu gửi lên không hợp lệ. Vui lòng kiểm tra lại thông tin.";
        } else if (apiResponse.status === 401) {
          errorMessage = "Bạn cần đăng nhập để thực hiện chức năng này.";
        } else if (apiResponse.status === 500) {
          errorMessage =
            "Lỗi server. Vui lòng thử lại sau hoặc liên hệ hỗ trợ.";
        } else if (apiResponse.status === 0 || !apiResponse.status) {
          errorMessage =
            "Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng.";
        }

        throw new Error(errorMessage);
      }

      const bloodRequest = apiResponse.data;

      // Create notification for member about successful submission
      try {
        const notificationResult = await bloodRequestNotificationService.createBloodRequestSubmissionNotification({
          userId: currentUser.id,
          requestId: bloodRequest.id || bloodRequest.requestId,
        });

        if (!notificationResult.success) {
          console.error("Failed to create submission notification:", notificationResult.error);
        }
      } catch (notificationError) {
        console.error("Error creating submission notification:", notificationError);
        // Don't fail the whole process if notification fails
      }

      setSubmissionResult({
        status: "success",
        message: "GỬI YÊU CẦU THÀNH CÔNG",
        description:
          "Yêu cầu máu của bạn đã được gửi đến bác sĩ khoa Huyết học. Bạn sẽ nhận được phản hồi sớm.",
        requestId: bloodRequest.id || bloodRequest.requestId,
        data: {
          bloodType: formData.bloodType,
          quantity: formData.quantity,
          unit: formData.unit || "ml",
          patientName: formData.patientName,
        },
      });
    } catch (error) {
      console.error("Error submitting blood request:", error);
      setSubmissionResult({
        status: "error",
        message: "GỬI YÊU CẦU THẤT BẠI",
        description:
          error.message ||
          "Có lỗi xảy ra khi gửi yêu cầu. Vui lòng thử lại sau hoặc liên hệ hỗ trợ.",
      });
    } finally {
      setLoading(false);
    }
  };

  // Success/Error Result Component
  if (submissionResult) {
    return (
      <>
        <MemberNavbar />
        <BloodRequestResultDisplay
          submissionResult={submissionResult}
          onRetry={() => setSubmissionResult(null)}
        />
      </>
    );
  }

  // Step Progress Component
  const StepProgress = () => (
    <div className="steps-container">
      <div className="steps-wrapper">
        {steps.map((step, index) => {
          const Icon = step.icon;
          const isActive = index === currentStep;
          const isCompleted = index < currentStep;

          return (
            <div key={index} className="step-item">
              <div
                className={`step-indicator ${isActive ? "active" : ""} ${
                  isCompleted ? "completed" : ""
                }`}
              >
                <div
                  className={`step-circle ${isActive ? "active" : ""} ${
                    isCompleted ? "completed" : ""
                  }`}
                >
                  {isCompleted ? <FaCheckCircle /> : <Icon />}
                </div>
                <div className="step-info">
                  <div
                    className={`step-title ${isActive ? "active" : ""} ${
                      isCompleted ? "completed" : ""
                    }`}
                  >
                    {step.title}
                  </div>
                  <div
                    className={`step-description ${isActive ? "active" : ""}`}
                  >
                    {step.description}
                  </div>
                </div>
              </div>
              {index < steps.length - 1 && (
                <div
                  className={`step-connector ${isCompleted ? "completed" : ""}`}
                />
              )}
            </div>
          );
        })}
      </div>
      <div className="progress-bar-container">
        <ProgressBar
          now={(currentStep / (steps.length - 1)) * 100}
          variant={currentStep === steps.length - 1 ? "success" : "primary"}
          className="custom-progress"
        />
      </div>
    </div>
  );

  // Loading screen while checking pending requests
  if (isCheckingPending) {
    return (
      <div className="blood-request-form-page">
        <MemberNavbar />
        <Container className="py-5">
          <Row className="justify-content-center">
            <Col lg={6} className="text-center">
              <Card className="p-5">
                <Spinner
                  animation="border"
                  variant="primary"
                  className="mb-3"
                />
                {/* <h4>Đang kiểm tra trạng thái đơn đăng ký...</h4>
                <p className="text-muted">Vui lòng chờ trong giây lát</p> */}
              </Card>
            </Col>
          </Row>
        </Container>
      </div>
    );
  }

  // Block access if profile is incomplete
  if (!profileComplete && !isCheckingProfile) {
    return (
      <div className="blood-request-form-page">
        <MemberNavbar />
        <Container className="py-5">
          <Row className="justify-content-center">
            <Col lg={8}>
              <Alert variant="info" className="text-center p-4">
                <FaExclamationTriangle size={48} className="mb-3 text-info" />
                <h4>Cần cập nhật hồ sơ cá nhân</h4>
                <p>
                  Để đảm bảo quy trình đăng ký nhận máu diễn ra thuận lợi, bạn
                  cần cập nhật đầy đủ thông tin cá nhân trước khi tiếp tục.
                </p>
                <div className="d-flex gap-3 justify-content-center mt-4">
                  <Button variant="primary" onClick={() => setShowModal(true)}>
                    Xem thông tin cần bổ sung
                  </Button>
                  <Button
                    variant="outline-primary"
                    onClick={() => navigate("/member/profile")}
                  >
                    Cập nhật hồ sơ
                  </Button>
                  <Button
                    variant="outline-secondary"
                    onClick={() => navigate("/member")}
                  >
                    <FaHome className="me-2" />
                    Về trang chủ
                  </Button>
                </div>
              </Alert>
            </Col>
          </Row>
        </Container>

        {/* Unified Modal */}
        <UnifiedModal
          visible={showModal}
          onClose={() => setShowModal(false)}
          type={modalType}
          context={modalContext}
          profileCheckResult={profileCheckResult}
          pendingRequest={pendingRequest}
          onGoToProfile={() => {
            setShowModal(false);
            navigate("/member/profile");
          }}
          onViewHistory={() => {
            setShowModal(false);
            navigate("/member/activity-history");
          }}
        />
      </div>
    );
  }

  // Block access if user has pending request
  if (!canCreateRequest && pendingRequest) {
    return (
      <div className="blood-request-form-page">
        <MemberNavbar />
        <Container className="py-5">
          <Row className="justify-content-center">
            <Col lg={8}>
              <Alert variant="warning" className="text-center p-4">
                <FaExclamationTriangle
                  size={48}
                  className="mb-3 text-warning"
                />
                <h4>Không thể tạo đơn đăng ký mới</h4>
                <p>
                  Bạn đã có đơn đăng ký nhận máu đang chờ xử lý. Để tránh spam,
                  hệ thống chỉ cho phép một đơn đăng ký tại một thời điểm.
                </p>
                <div className="d-flex gap-3 justify-content-center mt-4">
                  <Button variant="primary" onClick={() => setShowModal(true)}>
                    Xem chi tiết đơn hiện tại
                  </Button>
                  <Button
                    variant="outline-secondary"
                    onClick={() => navigate("/member/activity-history")}
                  >
                    Xem lịch sử hoạt động
                  </Button>
                  <Button
                    variant="outline-primary"
                    onClick={() => navigate("/member")}
                  >
                    <FaHome className="me-2" />
                    Về trang chủ
                  </Button>
                </div>
              </Alert>
            </Col>
          </Row>
        </Container>
      </div>
    );
  }

  return (
    <div className="blood-request-form-page">
      <MemberNavbar />

      <div className="request-content">
        {/* Hero Header Section */}
        <div className="hero-section">
          <div className="hero-decoration-1" />
          <div className="hero-decoration-2" />
          <div className="hero-content">
            <h1 className="hero-title">🩸 Đăng ký nhận máu</h1>
            <p className="hero-subtitle">
              Hoàn thành các bước để yêu cầu máu từ ngân hàng máu
            </p>
          </div>
        </div>

        {/* Steps Navigation */}
        <div className="steps-navigation">
          <StepProgress />
        </div>

        <Container className="form-container">
          <Row className="justify-content-center">
            <Col lg={10} xl={9}>
              <Card className="main-form-card">
                <Card.Header className="form-header">
                  <div className="header-content">
                    <div className="header-icon">
                      {React.createElement(steps[currentStep].icon)}
                    </div>
                    <div className="header-text">
                      <h4 className="header-title">
                        {steps[currentStep].title}
                      </h4>
                      <p className="header-description">
                        {steps[currentStep].description}
                      </p>
                    </div>
                  </div>
                  <div className="step-counter">
                    <Badge bg="light" text="dark">
                      Bước {currentStep + 1} / {steps.length}
                    </Badge>
                  </div>
                </Card.Header>
                <Card.Body className="form-body">
                  <Form noValidate onSubmit={handleSubmit}>
                    {/* Step 0: Blood Information */}
                    {currentStep === 0 && (
                      <div className="step-content blood-info-step">
                        <div className="section-title">
                          <FaHeart className="section-icon" />
                          <h5>Thông tin về máu cần thiết</h5>
                        </div>

                        <div className="form-grid">
                          <div className="form-group-row">
                            <div className="form-group">
                              <Form.Label className="form-label">
                                Nhóm máu <span className="required">*</span>
                              </Form.Label>
                              <Form.Select
                                value={formData.bloodType}
                                onChange={(e) =>
                                  handleInputChange("bloodType", e.target.value)
                                }
                                className={`form-select ${
                                  shouldShowFieldError(
                                    "bloodType",
                                    !formData.bloodType
                                  )
                                    ? "is-invalid"
                                    : ""
                                }`}
                              >
                                <option value="">Chọn nhóm máu</option>
                                <option value="O+">O+</option>
                                <option value="O-">O-</option>
                                <option value="A+">A+</option>
                                <option value="A-">A-</option>
                                <option value="B+">B+</option>
                                <option value="B-">B-</option>
                                <option value="AB+">AB+</option>
                                <option value="AB-">AB-</option>
                              </Form.Select>
                              {shouldShowFieldError(
                                "bloodType",
                                !formData.bloodType
                              ) && (
                                <div className="error-message">
                                  Vui lòng chọn nhóm máu
                                </div>
                              )}
                            </div>

                            <div className="form-group">
                              <Form.Label className="form-label">
                                Thành phần máu{" "}
                                <span className="required">*</span>
                              </Form.Label>
                              <Form.Select
                                value={formData.componentId}
                                onChange={(e) =>
                                  handleInputChange(
                                    "componentId",
                                    e.target.value
                                  )
                                }
                                className={`form-select ${
                                  shouldShowFieldError(
                                    "componentId",
                                    !formData.componentId
                                  )
                                    ? "is-invalid"
                                    : ""
                                }`}
                              >
                                <option value="">Chọn thành phần máu</option>
                                {Object.entries(BLOOD_COMPONENT_MAP).map(
                                  ([id, name]) => (
                                    <option key={id} value={id}>
                                      {name}
                                    </option>
                                  )
                                )}
                              </Form.Select>
                              {shouldShowFieldError(
                                "componentId",
                                !formData.componentId
                              ) && (
                                <div className="error-message">
                                  Vui lòng chọn thành phần máu
                                </div>
                              )}
                            </div>
                          </div>

                          <div className="form-group-row quantity-row">
                            <div className="form-group flex-2">
                              <Form.Label className="form-label">
                                Số lượng <span className="required">*</span>
                                <span
                                  className="unit-note"
                                  style={{
                                    fontWeight: 400,
                                    color: "#888",
                                    fontSize: "0.95em",
                                    marginLeft: 8,
                                  }}
                                >
                                  (ml)
                                </span>
                              </Form.Label>
                              <Form.Control
                                type="number"
                                value={formData.quantity}
                                onChange={(e) =>
                                  handleInputChange("quantity", e.target.value)
                                }
                                placeholder="Nhập số lượng"
                                min="1"
                                className={`form-input ${
                                  shouldShowFieldError(
                                    "quantity",
                                    !formData.quantity
                                  )
                                    ? "is-invalid"
                                    : ""
                                }`}
                              />
                              {shouldShowFieldError(
                                "quantity",
                                !formData.quantity
                              ) && (
                                <div className="error-message">
                                  Vui lòng nhập số lượng
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Step 1: Patient Information */}
                    {currentStep === 1 && (
                      <div className="step-content patient-info-step">
                        <div className="section-title">
                          <FaUser className="section-icon" />
                          <h5>Thông tin bệnh nhân</h5>
                        </div>

                        <div className="form-grid">
                          <div className="form-group-row">
                            <div className="form-group flex-2">
                              <Form.Label className="form-label">
                                Tên bệnh nhân{" "}
                                <span className="required">*</span>
                              </Form.Label>
                              <Form.Control
                                type="text"
                                value={formData.patientName}
                                onChange={(e) =>
                                  handleInputChange(
                                    "patientName",
                                    e.target.value
                                  )
                                }
                                placeholder="Nhập tên bệnh nhân"
                                className={`form-input ${
                                  shouldShowFieldError(
                                    "patientName",
                                    !formData.patientName
                                  )
                                    ? "is-invalid"
                                    : ""
                                }`}
                              />
                              {shouldShowFieldError(
                                "patientName",
                                !formData.patientName
                              ) && (
                                <div className="error-message">
                                  Vui lòng nhập tên bệnh nhân
                                </div>
                              )}
                            </div>
                            <div className="form-group flex-1">
                              <Form.Label className="form-label">
                                Tuổi <span className="required">*</span>
                              </Form.Label>
                              <Form.Control
                                type="number"
                                value={formData.patientAge}
                                onChange={(e) =>
                                  handleInputChange(
                                    "patientAge",
                                    e.target.value
                                  )
                                }
                                placeholder="Tuổi"
                                min="1"
                                max="150"
                                className={`form-input ${
                                  shouldShowFieldError(
                                    "patientAge",
                                    !formData.patientAge ||
                                      formData.patientAge <= 0
                                  )
                                    ? "is-invalid"
                                    : ""
                                }`}
                              />
                              {shouldShowFieldError(
                                "patientAge",
                                !formData.patientAge || formData.patientAge <= 0
                              ) && (
                                <div className="error-message">
                                  {!formData.patientAge
                                    ? "Vui lòng nhập tuổi"
                                    : "Tuổi phải lớn hơn 0"}
                                </div>
                              )}
                            </div>
                            <div className="form-group flex-1">
                              <Form.Label className="form-label">
                                Giới tính <span className="required">*</span>
                              </Form.Label>
                              <Form.Select
                                value={formData.patientGender}
                                onChange={(e) =>
                                  handleInputChange(
                                    "patientGender",
                                    e.target.value
                                  )
                                }
                                className={`form-select ${
                                  shouldShowFieldError(
                                    "patientGender",
                                    !formData.patientGender
                                  )
                                    ? "is-invalid"
                                    : ""
                                }`}
                              >
                                <option value="">Chọn giới tính</option>
                                <option value="male">Nam</option>
                                <option value="female">Nữ</option>
                                <option value="other">Khác</option>
                              </Form.Select>
                              {shouldShowFieldError(
                                "patientGender",
                                !formData.patientGender
                              ) && (
                                <div className="error-message">
                                  Vui lòng chọn giới tính
                                </div>
                              )}
                            </div>
                          </div>

                          <div className="form-group">
                            <Form.Label className="form-label">
                              Mối quan hệ với bệnh nhân{" "}
                              <span className="required">*</span>
                            </Form.Label>
                            <Form.Select
                              value={formData.patientRelation}
                              onChange={(e) =>
                                handleInputChange(
                                  "patientRelation",
                                  e.target.value
                                )
                              }
                              className={`form-select ${
                                shouldShowFieldError(
                                  "patientRelation",
                                  !formData.patientRelation
                                )
                                  ? "is-invalid"
                                  : ""
                              }`}
                            >
                              <option value="">Chọn mối quan hệ</option>
                              <option value="self">Chính bản thân tôi</option>
                              <option value="family">Gia đình</option>
                              <option value="friend">Bạn bè</option>
                              
                              <option value="other">Khác</option>
                            </Form.Select>
                            {shouldShowFieldError(
                              "patientRelation",
                              !formData.patientRelation
                            ) && (
                              <div className="error-message">
                                Vui lòng chọn mối quan hệ với bệnh nhân
                              </div>
                            )}
                          </div>

                          {formData.patientRelation === "other" && (
                            <div className="form-group">
                              <Form.Label className="form-label">
                                Mối quan hệ khác{" "}
                                <span className="required">*</span>
                              </Form.Label>
                              <Form.Control
                                type="text"
                                placeholder="Nhập mối quan hệ cụ thể"
                                value={formData.patientRelationOther}
                                onChange={(e) =>
                                  handleInputChange(
                                    "patientRelationOther",
                                    e.target.value
                                  )
                                }
                                className={`form-input ${
                                  shouldShowFieldError(
                                    "patientRelationOther",
                                    formData.patientRelation === "other" &&
                                      !formData.patientRelationOther.trim()
                                  )
                                    ? "is-invalid"
                                    : ""
                                }`}
                              />
                              {shouldShowFieldError(
                                "patientRelationOther",
                                formData.patientRelation === "other" &&
                                  !formData.patientRelationOther.trim()
                              ) && (
                                <div className="error-message">
                                  Vui lòng nhập mối quan hệ cụ thể
                                </div>
                              )}
                            </div>
                          )}

                          <div className="form-group">
                            <Form.Label className="form-label">
                              Tình trạng bệnh lý{" "}
                              <span className="required">*</span>
                            </Form.Label>
                            <Form.Control
                              as="textarea"
                              rows={4}
                              value={formData.medicalCondition}
                              onChange={(e) =>
                                handleInputChange(
                                  "medicalCondition",
                                  e.target.value
                                )
                              }
                              placeholder="Mô tả chi tiết tình trạng bệnh lý cần truyền máu, bao gồm: chẩn đoán, triệu chứng, mức độ nghiêm trọng, thời gian cần truyền máu..."
                              className={`form-textarea ${
                                shouldShowFieldError(
                                  "medicalCondition",
                                  !formData.medicalCondition
                                )
                                  ? "is-invalid"
                                  : ""
                              }`}
                            />
                            {shouldShowFieldError(
                              "medicalCondition",
                              !formData.medicalCondition
                            ) && (
                              <div className="error-message">
                                Vui lòng mô tả tình trạng bệnh lý
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Step 2: Medical Information */}
                    {currentStep === 2 && (
                      <div className="step-content medical-info-step">
                        <div className="section-title">
                          <FaStethoscope className="section-icon" />
                          <h5>Thông tin y tế</h5>
                        </div>

                        <div className="form-grid">
                          <div className="form-group-row">
                            <div className="form-group">
                              <Form.Label className="form-label">
                                Cơ sở y tế đang khám chữa bệnh{" "}
                                <span className="required">*</span>
                              </Form.Label>
                              <Form.Control
                                type="text"
                                value={formData.hospitalName}
                                onChange={(e) =>
                                  handleInputChange(
                                    "hospitalName",
                                    e.target.value
                                  )
                                }
                                placeholder="Tên cơ sở y tế đang khám chữa bệnh"
                                className={`form-input ${getFieldValidationClass(
                                  "hospitalName",
                                  !formData.hospitalName
                                )}`}
                              />
                              {shouldShowFieldError(
                                "hospitalName",
                                !formData.hospitalName
                              ) && (
                                <div className="error-message">
                                  Vui lòng nhập cơ sở y tế đang khám chữa bệnh
                                </div>
                              )}
                            </div>

                            <div className="form-group">
                              <Form.Label className="form-label">
                                Tên bác sĩ điều trị{" "}
                                <span className="required">*</span>
                              </Form.Label>
                              <Form.Control
                                type="text"
                                value={formData.doctorName}
                                onChange={(e) =>
                                  handleInputChange(
                                    "doctorName",
                                    e.target.value
                                  )
                                }
                                placeholder="Tên bác sĩ"
                                className={`form-input ${getFieldValidationClass(
                                  "doctorName",
                                  !formData.doctorName
                                )}`}
                              />
                              {shouldShowFieldError(
                                "doctorName",
                                !formData.doctorName
                              ) && (
                                <div className="error-message">
                                  Vui lòng nhập tên bác sĩ điều trị
                                </div>
                              )}
                            </div>
                          </div>
                          <div className="form-group">
                            <Form.Label className="form-label">
                              Số điện thoại bác sĩ{" "}
                              <span className="required">*</span>
                            </Form.Label>
                            <Form.Control
                              type="tel"
                              value={formData.doctorPhone}
                              onChange={(e) =>
                                handleInputChange("doctorPhone", e.target.value)
                              }
                              placeholder="Số điện thoại bác sĩ (10 số)"
                              maxLength="10"
                              className={`form-input ${
                                shouldShowFieldError(
                                  "doctorPhone",
                                  !formData.doctorPhone ||
                                    formData.doctorPhone.length !== 10
                                )
                                  ? "is-invalid"
                                  : ""
                              }`}
                            />
                            {shouldShowFieldError(
                              "doctorPhone",
                              !formData.doctorPhone ||
                                formData.doctorPhone.length !== 10
                            ) && (
                              <div className="error-message">
                                {!formData.doctorPhone
                                  ? "Vui lòng nhập số điện thoại bác sĩ"
                                  : "Số điện thoại phải đủ 10 số"}
                              </div>
                            )}
                          </div>
                          <div className="form-group pdf-upload-section">
                            <Form.Label className="form-label">
                              Báo cáo y tế/Chẩn đoán (PDF){" "}
                              <span className="required">*</span>
                            </Form.Label>
                            <div className="pdf-upload-container">
                              <div className="upload-area">
                                <Form.Control
                                  type="file"
                                  accept="application/pdf"
                                  onChange={async (e) => {
                                    const file = e.target.files[0];
                                    if (file) {
                                      // Lưu file để gửi binary data
                                      setSelectedPdfFile(file);

                                      // Hiển thị loading
                                      setUploadingPdf(true);
                                      const result =
                                        await uploadPdfMedicalReport(file);
                                      setUploadingPdf(false);
                                      if (result.success) {
                                        handleInputChange(
                                          "medicalReports",
                                          result.url
                                        );
                                        setPdfUploadError("");
                                      } else {
                                        setPdfUploadError(result.message);
                                        handleInputChange("medicalReports", "");
                                        setSelectedPdfFile(null);
                                      }
                                    }
                                  }}
                                  className={`pdf-file-input ${
                                    shouldShowFieldError(
                                      "medicalReports",
                                      !formData.medicalReports
                                    )
                                      ? "is-invalid"
                                      : ""
                                  }`}
                                  style={{ display: "none" }}
                                  id="pdf-upload"
                                />
                                <label
                                  htmlFor="pdf-upload"
                                  className="pdf-upload-label"
                                >
                                  <div className="upload-content">
                                    <div className="upload-icon">📁</div>
                                    <div className="upload-text">
                                      <strong>Chọn file PDF</strong>
                                      <br />
                                      <span className="upload-subtext">
                                        Kéo thả file hoặc nhấp để chọn
                                      </span>
                                    </div>
                                  </div>
                                </label>
                              </div>

                              {/* Upload Status */}
                              {uploadingPdf && (
                                <div className="upload-status uploading">
                                  <div className="status-icon">⏳</div>
                                  <div className="status-text">
                                    Đang upload file PDF...
                                  </div>
                                </div>
                              )}

                              {pdfUploadError && (
                                <div className="upload-status error">
                                  <div className="status-icon">❌</div>
                                  <div className="status-text">
                                    {pdfUploadError}
                                  </div>
                                </div>
                              )}

                              {formData.medicalReports &&
                                !pdfUploadError &&
                                selectedPdfFile && (
                                  <div className="upload-status success">
                                    <div className="status-icon">✅</div>
                                    <div className="status-content">
                                      <div className="status-text">
                                        <strong>{selectedPdfFile.name}</strong>
                                      </div>
                                      <div className="file-size">
                                        {(
                                          selectedPdfFile.size /
                                          1024 /
                                          1024
                                        ).toFixed(2)}{" "}
                                        MB
                                      </div>
                                    </div>
                                  </div>
                                )}

                              {shouldShowFieldError(
                                "medicalReports",
                                !formData.medicalReports
                              ) && (
                                <div className="upload-status error">
                                  <div className="status-icon">⚠️</div>
                                  <div className="status-text">
                                    Vui lòng upload báo cáo y tế/chẩn đoán (PDF)
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Navigation Buttons */}
                    <div className="form-actions">
                      <div className="actions-left">
                        {currentStep > 0 && (
                          <Button
                            variant="outline-secondary"
                            onClick={handlePrev}
                            disabled={loading}
                            className="btn-back"
                          >
                            <FaArrowLeft className="me-2" />
                            Quay lại
                          </Button>
                        )}
                      </div>

                      <div className="actions-right">
                        {currentStep < steps.length - 1 ? (
                          <Button
                            variant="primary"
                            onClick={handleNext}
                            disabled={loading}
                            className="btn-next"
                          >
                            Tiếp theo
                            <FaArrowRight className="ms-2" />
                          </Button>
                        ) : (
                          <Button
                            variant="success"
                            type="submit"
                            disabled={loading}
                            className="btn-submit"
                          >
                            {loading ? (
                              <>
                                <Spinner
                                  as="span"
                                  animation="border"
                                  size="sm"
                                  role="status"
                                  className="me-2"
                                />
                                Đang gửi...
                              </>
                            ) : (
                              <>
                                <FaCheckCircle className="me-2" />
                                Gửi yêu cầu máu
                              </>
                            )}
                          </Button>
                        )}
                      </div>
                    </div>
                  </Form>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Container>
      </div>

      {/* Profile Incomplete Modal */}
      <ProfileIncompleteModal
        visible={showProfileIncompleteModal}
        onClose={() => setShowProfileIncompleteModal(false)}
        profileCheckResult={profileCheckResult}
        onGoToProfile={() => {
          setShowProfileIncompleteModal(false);
          navigate("/member/profile");
        }}
      />
    </div>
  );
};

export default BloodRequestFormPage;
