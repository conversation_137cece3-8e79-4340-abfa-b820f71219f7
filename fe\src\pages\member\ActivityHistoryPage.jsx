import { useEffect, useState } from "react";
import { Card, Modal, Space, Typography, Button, Pagination } from "antd";
import {
  HeartFilled,
  EyeOutlined,
  DeleteOutlined,
  FileTextOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  EnvironmentOutlined,
  UserOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import MemberNavbar from "../../components/member/MemberNavbar";
import DetailedStatusTimeline from "../../components/member/DetailedStatusTimeline";
import ActivityDetailModal from "../../components/member/ActivityDetailModal";
import ModernPageHeader from "../../components/member/ModernPageHeader";
import ActivityStatisticsOverview from "../../components/member/ActivityStatisticsOverview";
import ActivityFilterSection from "../../components/member/ActivityFilterSection";
import ActivityLoadingState from "../../components/member/ActivityLoadingState";
import ActivityEmptyState from "../../components/member/ActivityEmptyState";
import StatusBadge, { DonationStatusBadge, RequestStatusBadge } from "../../components/common/StatusBadge";
import useActivityData from "../../hooks/useActivityData";
import useActivityModals from "../../hooks/useActivityModals";
import usePagination from "../../hooks/usePagination";
import { DONATION_STATUS } from "../../constants/systemConstants";
import "../../styles/pages/ActivityHistoryPage.scss";

const { Text } = Typography;

// Helper function
const formatDate = (dateString) => {
  if (!dateString) return "Chưa có thông tin";
  return new Date(dateString).toLocaleDateString("vi-VN");
};

const getStatusInfo = (status, type) => {
  if (type === "donation") {
    switch (status) {
      case DONATION_STATUS.REGISTERED:
        return { color: "blue", text: "Đã đăng ký" };
      case DONATION_STATUS.HEALTH_CHECKED:
        return { color: "orange", text: "Đã khám sức khỏe" };
      case DONATION_STATUS.NOT_ELIGIBLE_HEALTH:
        return { color: "red", text: "Không đủ điều kiện (sau khám)" };
      case DONATION_STATUS.DONATED:
        return { color: "purple", text: "Đã hiến máu" };
      case DONATION_STATUS.BLOOD_TESTED:
        return { color: "cyan", text: "Đã xét nghiệm máu" };
      case DONATION_STATUS.NOT_ELIGIBLE_TEST:
        return { color: "red", text: "Không đủ điều kiện (sau xét nghiệm)" };
      case DONATION_STATUS.COMPLETED:
        return { color: "green", text: "Hoàn thành" };
      case DONATION_STATUS.STORED:
        return { color: "green", text: "Đã nhập kho (hoàn thành)" };
      default:
        return { color: "default", text: "Không xác định" };
    }
  }
  return { color: "default", text: "Không xác định" };
};

/**
 * ActivityHistoryPage - Refactored version
 * Giữ nguyên giao diện và logic từ file gốc nhưng tối ưu hóa cấu trúc code
 */
const ActivityHistoryPage = () => {
  // Date range state
  const [dateRange, setDateRange] = useState(null);

  // Sử dụng custom hooks để tách logic
  const {
    activities,
    filteredActivities,
    loading,
    filter,
    searchId,
    donationCount,
    requestCount,
    completedCount,
    setFilter,
    setSearchId,
    forceRefresh,
    handleCancelAppointment,
  } = useActivityData();

  const {
    selectedActivity,
    showWorkflowModal,
    showDetailModal,
    loadingDetails,
    handleViewDetails,
    handleViewWorkflow,
    closeDetailModal,
    closeWorkflowModal,
  } = useActivityModals();

  // Filter activities by date range
  const getDateFilteredActivities = () => {
    let filtered = filteredActivities;

    if (dateRange && dateRange[0] && dateRange[1]) {
      const startDate = dateRange[0].startOf('day');
      const endDate = dateRange[1].endOf('day');

      filtered = filtered.filter((activity) => {
        const activityDate = activity.createdAt || activity.appointmentDate;
        if (!activityDate) return false;

        const date = new Date(activityDate);
        return date >= startDate.toDate() && date <= endDate.toDate();
      });
    }

    return filtered;
  };

  const finalFilteredActivities = getDateFilteredActivities();

  // Pagination cho activities
  const {
    currentPage,
    setCurrentPage,
    pageSize,
    paginatedData: paginatedActivities,
    total: totalActivities,
  } = usePagination(finalFilteredActivities, 10); // 10 activities per page

  // Reset pagination when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [filter, searchId, dateRange, setCurrentPage]);

  // Force refresh when component mounts to ensure latest data
  useEffect(() => {
    console.log("🔄 ActivityHistoryPage mounted - force refreshing data");
    forceRefresh();
  }, [forceRefresh]);

  // Auto refresh when user comes back to the page
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log("🔄 Page became visible - refreshing activity history");
        forceRefresh();
      }
    };

    const handleFocus = () => {
      console.log("🔄 Page focused - refreshing activity history");
      forceRefresh();
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);
    window.addEventListener("focus", handleFocus);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      window.removeEventListener("focus", handleFocus);
    };
  }, [forceRefresh]);

  return (
    <div className="activity-history-page">
      <MemberNavbar />

      <div className="activity-content">
        {/* Header Section - Sử dụng ModernPageHeader mới với variant gradient */}
        <ModernPageHeader
          loading={loading}
          onReload={forceRefresh}
          variant="gradient"
          theme="light"
        />

        {/* Statistics Overview - Sử dụng component riêng */}
        <ActivityStatisticsOverview
          activities={activities}
          donationCount={donationCount}
          requestCount={requestCount}
          completedCount={completedCount}
        />

        {/* Filter Section - Sử dụng component riêng với search ID và date range */}
        <ActivityFilterSection
          filter={filter}
          onFilterChange={setFilter}
          activities={activities}
          donationCount={donationCount}
          requestCount={requestCount}
          loading={loading}
          searchId={searchId}
          onSearchIdChange={setSearchId}
          dateRange={dateRange}
          onDateRangeChange={setDateRange}
        />

        {/* Activities List - Giữ nguyên logic render từ file gốc */}
        {loading ? (
          <ActivityLoadingState />
        ) : finalFilteredActivities.length === 0 ? (
          <ActivityEmptyState filter={filter} />
        ) : (
          <div className="activities-list">
            {paginatedActivities.map((activity) => {
              const isCancelled = activity.isCancelled;

              return (
                <Card
                  key={activity.id}
                  className={`activity-card ${isCancelled ? "cancelled" : ""} ${activity.type
                    }`}
                  hoverable={!isCancelled}
                >
                  {/* Activity Header - Giữ nguyên từ file gốc */}
                  <div className="activity-header">
                    <div className="activity-info">
                      <div className="activity-title-wrapper">
                        <div className="activity-icon">
                          {activity.type === "donation" ? (
                            <HeartFilled />
                          ) : (
                            <FileTextOutlined />
                          )}
                        </div>
                        <div className="activity-title-content">
                          <h4 className="activity-title">{activity.title}</h4>
                          <Text className="activity-date">
                            <CalendarOutlined className="date-icon" />
                            Tạo lúc: {formatDate(activity.createdAt)}
                          </Text>
                        </div>
                      </div>
                    </div>
                    <div className="activity-status">
                      {isCancelled ? (
                        <StatusBadge
                          status="cancelled"
                          type={activity.type}
                          customText="ĐÃ HỦY"
                          size="default"
                        />
                      ) : activity.type === "donation" ? (
                        <DonationStatusBadge
                          status={activity.displayStatus || activity.status}
                          size="default"
                        />
                      ) : (
                        <RequestStatusBadge
                          status={activity.displayStatus || activity.status}
                          size="default"
                        />
                      )}
                    </div>
                  </div>


                  <div className="activity-details">
                    {/* Appointment Info for Donations */}
                    {activity.type === "donation" && (
                      <div className="detail-section">
                        <h4>
                          <Space>
                            <CalendarOutlined style={{ color: "#722ed1" }} />
                            <span>Thông tin lịch hẹn</span>
                          </Space>
                        </h4>
                        <div className="appointment-info">
                          <div className="info-item">
                            <CalendarOutlined className="info-icon" />
                            <span className="info-label">Ngày hẹn:</span>
                            <span className="info-value">
                              {formatDate(activity.appointmentDate)}
                            </span>
                          </div>
                          <div className="info-item">
                            <ClockCircleOutlined className="info-icon" />
                            <span className="info-label">Khung giờ:</span>
                            <span className="info-value">
                              {activity.timeSlot || "Chưa xác định"}
                            </span>
                          </div>
                          <div className="info-item">
                            <EnvironmentOutlined className="info-icon" />
                            <span className="info-label">Địa điểm:</span>
                            <span className="info-value">
                              {activity.location}
                            </span>
                          </div>
                        </div>
                      </div>
                    )}



                    {/* Patient Info for Requests */}
                    {activity.type === "request" && activity.patientName && (
                      <div className="detail-section">
                        <h4>
                          <Space>
                            <UserOutlined style={{ color: "#fa8c16" }} />
                            <span>Thông tin bệnh nhân</span>
                          </Space>
                        </h4>
                        <div className="patient-info">
                          <div className="info-item">
                            <UserOutlined className="info-icon" />
                            <span className="info-label">Bệnh nhân:</span>
                            <span className="info-value">
                              {activity.patientName}
                            </span>
                          </div>
                          {activity.hospitalName && (
                            <div className="info-item">
                              <EnvironmentOutlined className="info-icon" />
                              <span className="info-label">Bệnh viện:</span>
                              <span className="info-value">
                                {activity.hospitalName}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Activity Actions - Giữ nguyên từ file gốc */}
                  <div className="activity-actions">
                    <Space size={12} wrap>
                      <Button
                        type="default"
                        icon={<EyeOutlined />}
                        onClick={() => handleViewWorkflow(activity)}
                        className="btn btn-workflow"
                        size="middle"
                      >
                        Xem tiến trình
                      </Button>

                      <Button
                        type="primary"
                        icon={<FileTextOutlined />}
                        onClick={() => handleViewDetails(activity)}
                        className="btn btn-detail"
                        size="middle"
                      >
                        Chi tiết
                      </Button>

                      {activity.type === "donation" &&
                        !isCancelled &&
                        // Chỉ cho phép hủy khi còn ở trạng thái "Đăng ký" (chưa được chấp nhận)
                        activity.status === DONATION_STATUS.REGISTERED && (
                          <Button
                            type="default"
                            danger
                            icon={<DeleteOutlined />}
                            onClick={() => handleCancelAppointment(activity.id)}
                            className="btn btn-danger"
                            size="middle"
                          >
                            Hủy lịch
                          </Button>
                        )}



                      {isCancelled && (
                        <Text
                          type="secondary"
                          disabled
                          className="cancelled-note"
                        >
                          <Space>
                            <ExclamationCircleOutlined />
                            <span>Không thể thao tác với lịch hẹn đã hủy</span>
                          </Space>
                        </Text>
                      )}

                      {/* Thông báo khi không thể hủy lịch do đã được chấp nhận */}
                      {activity.type === "donation" &&
                        !isCancelled &&
                        activity.status !== DONATION_STATUS.REGISTERED &&
                        activity.status !== DONATION_STATUS.COMPLETED &&
                        activity.status !== DONATION_STATUS.STORED && (
                          <Text
                            type="secondary"
                            className="cannot-cancel-note"
                            style={{ color: '#fa8c16' }}
                          >
                            {/* <Space>
                              <ExclamationCircleOutlined />
                              <span>Không thể hủy lịch: Đã được bác sĩ chấp nhận</span>
                            </Space> */}
                          </Text>
                        )}
                    </Space>
                  </div>
                </Card>
              );
            })}

            {/* Pagination */}
            {totalActivities > pageSize && (
              <div className="activities-pagination">
                <Pagination
                  current={currentPage}
                  pageSize={pageSize}
                  total={totalActivities}
                  onChange={setCurrentPage}
                  showSizeChanger={false}
                  showQuickJumper={true}
                  showTotal={(total, range) =>
                    `${range[0]}-${range[1]} của ${total} hoạt động`
                  }
                  className="activity-pagination"
                />
              </div>
            )}
          </div>
        )}
      </div>

      {/* Detail Modal - Cập nhật để hiển thị thông tin sức khỏe chi tiết */}
      <ActivityDetailModal
        visible={showDetailModal}
        onCancel={closeDetailModal}
        activity={selectedActivity}
        formatDate={formatDate}
        loadingDetails={loadingDetails}
      />

      {/* Workflow Modal - Giữ nguyên từ file gốc */}
      <Modal
        title={
          <Space>
            <span className={`modal-title-icon ${selectedActivity?.type}`}>
              {selectedActivity?.type === "donation" ? (
                <HeartFilled />
              ) : (
                <FileTextOutlined />
              )}
            </span>
            <Text strong className="modal-title-text">
              Tiến trình{" "}
              {selectedActivity?.type === "donation"
                ? "hiến máu"
                : "yêu cầu máu"}
            </Text>
          </Space>
        }
        open={showWorkflowModal}
        onCancel={closeWorkflowModal}
        footer={[
          <Button
            key="close"
            onClick={closeWorkflowModal}
            className="workflow-close-button"
          >
            Đóng
          </Button>,
        ]}
        width={800}
        className="workflow-modal"
      >
        {selectedActivity && (
          <div>
            <Card size="small" className="workflow-activity-card">
              <Space size={12}>
                <div
                  className={`workflow-activity-icon ${selectedActivity.type}`}
                >
                  {selectedActivity.type === "donation" ? (
                    <HeartFilled />
                  ) : (
                    <FileTextOutlined />
                  )}
                </div>
                <div>
                  <Text strong className="workflow-activity-title">
                    {selectedActivity.title}
                  </Text>
                  <br />
                  <Text className="workflow-activity-date">
                    Tạo lúc: {formatDate(selectedActivity.createdAt)}
                  </Text>
                </div>
              </Space>
            </Card>

            <DetailedStatusTimeline
              activity={selectedActivity}
              workflowType={selectedActivity.type}
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ActivityHistoryPage;
