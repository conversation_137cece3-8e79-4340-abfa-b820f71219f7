# 🔄 Constants Migration Guide

## 📊 **Tình trạng hiện tại:**

### ✅ **Đã migration thành công:**
- `fe/src/components/common/ProtectedRoute.jsx` ✅
- `fe/src/components/shared/BloodInventoryViewPage.jsx` ✅  
- `fe/src/services/authService.js` ✅

### ✅ **Files đã migration hoàn tất:**
- ~~`fe/src/services/mockData.js`~~ - **ĐÃ XÓA** ✅
- Tất cả constants đã được dồn vào `systemConstants.js` ✅

## 🎯 **Chi<PERSON><PERSON> lược thống nhất:**

### 1. **API Constants (cho authService & API calls):**
```javascript
// Sử dụng: fe/src/constants/systemConstants.js
import { API_ROLES, API_REQUEST_STATUS, DEPARTMENT_IDS } from "../constants/systemConstants";

export const API_ROLES = {
  GUEST: "Guest",
  MEMBER: "1",           // Database values
  STAFF_DOCTOR: "2", 
  STAFF_BLOOD_MANAGER: "3",
  ADMIN: "4",
};
```

### 2. **UI Constants (cho components & display):**
```javascript
// Sử dụng: fe/src/constants/systemConstants.js
import { USER_ROLES, REQUEST_STATUS } from "../constants/systemConstants";

export const USER_ROLES = {
  GUEST: "Guest",
  MEMBER: "Member",      // Display values
  STAFF_DOCTOR: "Staff-Doctor",
  STAFF_BLOOD_MANAGER: "Staff-BloodManager", 
  ADMIN: "Admin",
};
```

### 3. **Mapping Functions:**
```javascript
// Auto-convert giữa API và UI formats
export const mapApiRoleToUiRole = (apiRole) => { ... };
export const mapUiRoleToApiRole = (uiRole) => { ... };
```

## 📋 **Checklist Migration:**

### ✅ **Completed:**
- [x] Tạo API_ROLES trong systemConstants
- [x] Tạo mapping functions
- [x] Migration ProtectedRoute.jsx
- [x] Migration BloodInventoryViewPage.jsx
- [x] Migration authService.js
- [x] Thêm DEPARTMENT_IDS vào systemConstants
- [x] Thêm API_REQUEST_STATUS vào systemConstants
- [x] **Migration tất cả 10 files còn lại từ mockData**
- [x] **XÓA HOÀN TOÀN mockData.js** 🗑️
- [x] **Dồn tất cả constants vào systemConstants.js** 📦

### 🔄 **Next Steps:**
- [ ] Test tất cả authentication flows
- [ ] Test role-based access control
- [ ] Kiểm tra blood inventory functionality
- [x] ~~Deprecated mockData.js~~ **ĐÃ XÓA** ✅
- [x] ~~Update documentation~~ **ĐÃ CẬP NHẬT** ✅

## 🚨 **Lưu ý quan trọng:**

### **Không được xóa mockData.js ngay lập tức vì:**
1. Có thể có files khác vẫn import từ đó
2. Cần test kỹ trước khi remove
3. Có thể cần fallback data

### **Format khác nhau:**
- **API**: `"1", "2", "3", "4"` (database values)
- **UI**: `"Member", "Staff-Doctor"` (display values)
- **Mapping**: Auto-convert giữa 2 formats

## 🧪 **Test Cases cần chạy:**

1. **Authentication:**
   - Login với các roles khác nhau
   - Role-based routing
   - Protected routes

2. **Blood Inventory:**
   - Manager features (history, import/export)
   - Doctor view (chỉ inventory)
   - Role checking logic

3. **API Calls:**
   - Đảm bảo API nhận đúng format numbers
   - Error handling

## 📁 **Files đã thay đổi:**

### **Core Constants:**
1. `fe/src/constants/systemConstants.js` - **THỐNG NHẤT TẤT CẢ CONSTANTS** 📦
2. ~~`fe/src/services/mockData.js`~~ - **ĐÃ XÓA** 🗑️

### **Migrated Files (13 files):**
3. `fe/src/components/common/ProtectedRoute.jsx` - Dùng API_ROLES
4. `fe/src/components/shared/BloodInventoryViewPage.jsx` - Dùng API_ROLES
5. `fe/src/services/authService.js` - Dùng API_ROLES
6. `fe/src/pages/manager/DonationProcessManagement.jsx` - Dùng BLOOD_GROUPS, RH_TYPES
7. `fe/src/utils/departmentUtils.js` - Dùng DEPARTMENT_IDS
8. `fe/src/components/doctor/DoctorSidebar.jsx` - Dùng DOCTOR_TYPES
9. `fe/src/components/doctor/blood-requests/CreateBloodRequestModal.jsx` - Dùng BLOOD_GROUPS, RH_TYPES
10. `fe/src/components/shared/BloodRequestDetailModal.jsx` - Dùng DOCTOR_TYPES
11. `fe/src/hooks/useBloodRequestManagement.js` - Dùng DOCTOR_TYPES, URGENCY_LEVELS
12. `fe/src/hooks/useDoctorDonorManagement.js` - Dùng DOCTOR_TYPES
13. `fe/src/utils/statusWorkflowService.js` - Dùng DOCTOR_TYPES
14. `fe/src/pages/manager/BloodRequestsPage.jsx` - Dùng API_REQUEST_STATUS, URGENCY_LEVELS
15. `fe/src/pages/manager/EmergencyRequestsManagement.jsx` - Dùng API_REQUEST_STATUS, URGENCY_LEVELS, BLOOD_GROUPS, RH_TYPES

## 🎉 **Kết quả:**

- ✅ **Thống nhất constants** - Tất cả trong systemConstants
- ✅ **Tương thích API** - Dùng đúng format numbers
- ✅ **Tương thích UI** - Dùng đúng format strings
- ✅ **Backward compatible** - Không break existing code
- ✅ **Maintainable** - Dễ maintain và extend

