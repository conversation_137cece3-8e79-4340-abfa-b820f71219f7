/**
 * Utility functions for Blood Request API data transformation
 */

/**
 * Prepare blood request data for API update
 * @param {Object} requestData - Current request data
 * @param {number} newStatus - New status to set
 * @param {string} rejectionReason - Rejection reason (optional, for status 3)
 * @returns {Object} Formatted data for API
 */
export const prepareBloodRequestUpdateData = (
  requestData,
  newStatus,
  rejectionReason = null
) => {
  if (!requestData) {
    throw new Error("Request data is required");
  }

  const updateData = {
    requestId: parseInt(
      requestData.requestId || requestData.id || requestData.requestID
    ),
    userId: parseInt(requestData.userId || requestData.userID || 0),
    patientId:
      requestData.patientId && requestData.patientId !== 0
        ? parseInt(requestData.patientId)
        : null,
    patientName: requestData.patientName || "",
    age: parseInt(requestData.age || 0),
    gender: requestData.gender || "",
    relationship: requestData.relationship || "",
    facilityName: requestData.facilityName || "",
    doctorName: requestData.doctorName || "",
    doctorPhone: requestData.doctorPhone || "",
    bloodGroup: requestData.bloodGroup || "",
    rhType: requestData.rhType || "",
    quantity: parseInt(requestData.quantity || 0),
    reason: requestData.reason || "",
    status: newStatus,
    createdTime: requestData.createdTime || new Date().toISOString(),
  };

  // Add rejection reason to separate 'note' field for rejected status
  // Do NOT modify the original 'reason' field (member's medical reason)
  if (newStatus === 3 && rejectionReason) {
    updateData.note = rejectionReason; // Doctor's rejection reason goes to note field
  }

  return updateData;
};

/**
 * Validate blood request data before API call
 * @param {Object} data - Data to validate
 * @returns {Object} Validation result
 */
export const validateBloodRequestData = (data) => {
  const errors = [];

  if (!data.requestId || data.requestId <= 0) {
    errors.push("Request ID is required and must be positive");
  }

  // patientId can be null for new patients
  if (
    data.patientId !== null &&
    (isNaN(data.patientId) || data.patientId < 0)
  ) {
    errors.push("Patient ID must be null or a positive number");
  }

  if (!data.patientName || data.patientName.trim() === "") {
    errors.push("Patient name is required");
  }

  if (!data.age || data.age <= 0) {
    errors.push("Patient age is required and must be positive");
  }

  if (!data.gender || data.gender.trim() === "") {
    errors.push("Patient gender is required");
  }

  if (!data.bloodGroup || data.bloodGroup.trim() === "") {
    errors.push("Blood group is required");
  }

  if (!data.rhType || data.rhType.trim() === "") {
    errors.push("RH type is required");
  }

  if (!data.quantity || data.quantity <= 0) {
    errors.push("Quantity is required and must be positive");
  }

  if (typeof data.status !== "number" || data.status < 0 || data.status > 4) {
    errors.push("Status must be a number between 0 and 4");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Log API request for debugging
 * @param {string} action - Action being performed
 * @param {string|number} requestId - Request ID
 * @param {Object} data - Data being sent
 */
export const logApiRequest = (action, requestId, data) => {
  if (process.env.NODE_ENV === "development") {
    console.group(`Blood Request API: ${action}`);
    console.log("Request ID:", requestId);
    console.log("Data:", data);
    console.groupEnd();
  }
};

/**
 * Log API response for debugging
 * @param {string} action - Action that was performed
 * @param {boolean} success - Whether the request was successful
 * @param {Object} response - API response
 */
export const logApiResponse = (action, success, response) => {
  if (process.env.NODE_ENV === "development") {
    console.group(`${success ? "SUCCESS" : "ERROR"} Blood Request API: ${action}`);
    console.log("Success:", success);
    console.log("Response:", response);
    console.groupEnd();
  }
};
