import React from "react";
import {
  Form,
  DatePicker,
  Radio,
  Button,
  Card,
  Row,
  Col,
  Space,
  Typography,
  Alert,
  Divider,
} from "antd";
import {
  CalendarOutlined,
  ClockCircleOutlined,
  ArrowLeftOutlined,
  CheckOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";

const { Title, Text } = Typography;

/**
 * Component hiển thị bước 3: Đặt lịch hẹn
 */
const AppointmentStep = ({
  appointmentData,
  distanceInfo,
  loading,
  onAppointmentChange,
  onSubmit,
  onBack,
  getTimeSlotText,
  healthSurvey,
  calculateEarliestDonationDate,
  selfReportedLastDonationDate,
}) => {
  const handleDateChange = (date) => {
    onAppointmentChange("preferredDate", date ? date.format("YYYY-MM-DD") : "");
  };

  const handleTimeSlotChange = (e) => {
    onAppointmentChange("timeSlot", e.target.value);
  };

  return (
    <Card
      title={
        <Space>
          <CalendarOutlined />
          <span>Đặt lịch hẹn hiến máu</span>
        </Space>
      }
      className="appointment-card"
    >
      <Alert
        message="Chúc mừng! Bạn đủ điều kiện hiến máu"
        description="Vui lòng chọn ngày và giờ phù hợp để đặt lịch hẹn hiến máu."
        type="success"
        icon={<CheckOutlined />}
        className="success-alert"
        showIcon
      />

      {/* Hiển thị thông báo về điều kiện 84 ngày nếu user đã hiến máu trước đó */}
      {healthSurvey?.hasDonatedBefore === true && selfReportedLastDonationDate && (() => {
        const lastDonationDate = dayjs(selfReportedLastDonationDate);
        // Chỉ hiển thị nếu ngày hợp lệ (không phải 0001-01-01)
        if (lastDonationDate.isValid() && !lastDonationDate.isBefore(dayjs("1900-01-01"))) {
          return (
            <Alert
              message="Lưu ý về khoảng cách hiến máu"
              description={`Bạn đã hiến máu vào ${lastDonationDate.format("DD/MM/YYYY")}. Cần chờ ít nhất 84 ngày giữa các lần hiến máu. Ngày sớm nhất có thể hiến lại: ${calculateEarliestDonationDate(selfReportedLastDonationDate)?.format("DD/MM/YYYY")}`}
              type="info"
              className="donation-interval-alert"
              showIcon
              style={{ marginTop: 16 }}
            />
          );
        }
        return null;
      })()}

      <Form layout="vertical">
        <Title level={4} className="section-title">
          Thông tin lịch hẹn
        </Title>

        <Row gutter={24}>
          <Col xs={24} md={12}>
            <Form.Item
              label={
                <span className="form-label">
                  <CalendarOutlined style={{ marginRight: 8 }} />
                  Chọn ngày hiến máu
                </span>
              }
              required
            >
              <DatePicker
                value={
                  appointmentData.preferredDate
                    ? dayjs(appointmentData.preferredDate)
                    : null
                }
                onChange={handleDateChange}
                disabledDate={(current) => {
                  // Không cho chọn ngày trong quá khứ
                  if (current && current < dayjs().startOf("day")) {
                    return true;
                  }

                  // Kiểm tra điều kiện 84 ngày nếu user đã hiến máu trước đó
                  if (healthSurvey?.hasDonatedBefore === true && selfReportedLastDonationDate) {
                    const lastDonationDate = dayjs(selfReportedLastDonationDate);

                    // Kiểm tra xem ngày có hợp lệ không (không phải 0001-01-01)
                    if (lastDonationDate.isValid() && !lastDonationDate.isBefore(dayjs("1900-01-01"))) {
                      const earliestDate = calculateEarliestDonationDate(selfReportedLastDonationDate);
                      if (earliestDate && current && current < earliestDate.startOf("day")) {
                        return true;
                      }
                    }
                  }

                  return false;
                }}
                className="datepicker-full"
                placeholder="Chọn ngày hiến máu"
                format="DD/MM/YYYY"
                size="large"
              />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <Form.Item
              label={
                <span className="form-label">
                  <ClockCircleOutlined style={{ marginRight: 8 }} />
                  Chọn khung giờ
                </span>
              }
              required
            >
              <Radio.Group
                value={appointmentData.timeSlot}
                onChange={handleTimeSlotChange}
                className="time-slot-group"
                size="large"
              >
                <Radio.Button value="morning" className="time-slot-option">
                  <div className="time-slot-content">
                    <div className="time-slot-title">Buổi sáng</div>
                    <div className="time-slot-time">7:00 - 12:00</div>
                  </div>
                </Radio.Button>
                <Radio.Button value="afternoon" className="time-slot-option">
                  <div className="time-slot-content">
                    <div className="time-slot-title">Buổi chiều</div>
                    <div className="time-slot-time">13:00 - 17:00</div>
                  </div>
                </Radio.Button>
              </Radio.Group>
            </Form.Item>
          </Col>
        </Row>

        <Divider />

        <Title level={4} className="section-title">
          Địa điểm hiến máu
        </Title>

        <div className="location-info">
          <div className="location-card">
            <div className="location-header">
              <Title level={5} className="location-name">
                Bệnh viện Đa khoa Ánh Dương
              </Title>
              <Text className="location-department">
                Khoa Huyết học - Tầng 2
              </Text>
            </div>
            <div className="location-details">
              <div className="location-address">
                <Text strong>Địa chỉ: </Text>
                <Text>Đường Cách Mạng Tháng 8, Quận 3, TP.HCM, Vietnam</Text>
              </div>
              {distanceInfo && (
                <div className="location-distance">
                  <Text strong>Khoảng cách: </Text>
                  <Text>{distanceInfo.formattedDistance}</Text>
                </div>
              )}
            </div>
          </div>
        </div>

        <Divider />



        {/* Navigation Buttons */}
        <div className="form-navigation">
          <Button
            size="large"
            onClick={onBack}
            icon={<ArrowLeftOutlined />}
            className="back-button"
          >
            Quay lại
          </Button>
          <Button
            type="primary"
            size="large"
            loading={loading}
            onClick={onSubmit}
            className="submit-button"
            disabled={!appointmentData.preferredDate || !appointmentData.timeSlot}
          >
            {loading ? "Đang đặt lịch..." : "Xác nhận đặt lịch"}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default AppointmentStep;
