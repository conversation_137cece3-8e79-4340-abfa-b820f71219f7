import React, { useState, useEffect } from "react";
import { Form } from "antd";
import DoctorLayout from "../../components/doctor/DoctorLayout";
import PageHeader from "../../components/doctor/PageHeader";
import "../../styles/pages/BlogManagementNew.scss";
import "../../styles/pages/admin/BlogManagement.module.scss";
import "../../styles/components/DoctorPageHeader.scss";
import {
  Table,
  Card,
  Button,
  Select,
  Input,
  Space,
  Modal,
  Tabs,
  DatePicker,
  Row,
  Col,
} from "antd";
import {
  PlusOutlined,
  CalendarOutlined,
  FileTextOutlined,
  ReloadOutlined,
} from "@ant-design/icons";

const { RangePicker } = DatePicker;
import BlogEditModal from "../../components/admin/blogs/BlogEditModal";
import BlogDetailModal from "../../components/admin/blogs/BlogDetailModal";
import BlogTableColumns from "../../components/admin/blogs/BlogTableColumns";
import authService from "../../services/authService";
import * as bloodArticleService from "../../services/bloodArticleService";
import * as newsService from "../../services/newsService";
import userInfoService from "../../services/userInfoService";
import { useNavigate, useLocation } from "react-router-dom";
import { getAllTags, getNewsTags } from "../../services/tagService";
import { getNewsId, getNewsUserId, findNewsById } from "../../utils/newsUtils";
import {
  getArticleId,
  getArticleUserId,
  findArticleById,
} from "../../utils/articleUtils";
import { toast } from "../../utils/toastUtils";
import dayjs from "dayjs";
import isBetween from "dayjs/plugin/isBetween";

// Extend dayjs with isBetween plugin
dayjs.extend(isBetween);

const { Option } = Select;

const BlogManagement = () => {
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [dateRange, setDateRange] = useState(null);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedBlog, setSelectedBlog] = useState(null);
  const [editImage, setEditImage] = useState(null);
  const [form] = Form.useForm();
  const location = useLocation();
  const params = new URLSearchParams(location.search);
  const initialTab = params.get("tab") === "news" ? "Tin tức" : "Tài liệu";
  const [activeTab, setActiveTab] = useState(initialTab);
  const [tags, setTags] = useState([]);
  const [tagsLoading, setTagsLoading] = useState(false);

  // Lấy currentUser từ authService, nếu null thì lấy từ localStorage
  let currentUser = authService.getCurrentUser();
  if (!currentUser) {
    try {
      const userData = localStorage.getItem("currentUser");
      if (userData) {
        currentUser = JSON.parse(userData);
      }
    } catch (error) {
      console.error(
        "Error loading currentUser from localStorage in Doctor:",
        error
      );
    }
  }

  const [articleLoading, setArticleLoading] = useState(false);
  const [articles, setArticles] = useState([]);
  const [newsLoading, setNewsLoading] = useState(false);
  const [news, setNews] = useState([]);
  const [userMap, setUserMap] = useState({});
  const navigate = useNavigate();

  useEffect(() => {
    userInfoService
      .getAllUsers()
      .then((usersData) => {
        const tempUserMap = {};
        if (Array.isArray(usersData)) {
          usersData.forEach((user) => {
            const userId = user.id || user.userId || user.userID;
            const userName =
              user.name ||
              user.fullName ||
              user.username ||
              user.email ||
              `User ${userId}`;
            tempUserMap[userId] = userName;
          });
        }
        setUserMap(tempUserMap);
      })
      .catch((error) => {
        console.error("Error fetching users:", error);
        setUserMap({});
      });
  }, []);

  useEffect(() => {
    if (activeTab === "Tài liệu") {
      setArticleLoading(true);
      bloodArticleService
        .getBloodArticles()
        .then((articlesData) => {
          setArticles(Array.isArray(articlesData) ? articlesData : []);
        })
        .catch((error) => {
          console.error("Error fetching data:", error);
          toast.error("Không thể tải danh sách tài liệu");
        })
        .finally(() => setArticleLoading(false));
    }
  }, [activeTab]);

  useEffect(() => {
    if (activeTab === "Tin tức") {
      setNewsLoading(true);
      newsService
        .fetchAllNews()
        .then((data) => {
          setNews(Array.isArray(data) ? data : []);
        })
        .catch((error) => {
          console.error("Error fetching news:", error);
          toast.error("Không thể tải danh sách tin tức");
        })
        .finally(() => setNewsLoading(false));
    }
  }, [activeTab]);

  // Load tags based on active tab
  useEffect(() => {
    const loadTags = async () => {
      setTagsLoading(true);
      try {
        let tagsData = [];
        if (activeTab === "Tài liệu") {
          tagsData = await getAllTags();
        } else if (activeTab === "Tin tức") {
          tagsData = await getNewsTags();
        }
        setTags(Array.isArray(tagsData) ? tagsData : []);
      } catch (error) {
        console.error("Error loading tags:", error);
        setTags([]);
      } finally {
        setTagsLoading(false);
      }
    };

    loadTags();
  }, [activeTab]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  const tabCategories = [
    { key: "Tài liệu", label: "Tài liệu" },
    { key: "Tin tức", label: "Tin tức" },
  ];

  const getFilteredNews = () =>
    news.filter((item) => {
      // Search filter
      const matchesSearch =
        searchTerm === "" ||
        item.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.content?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.tags &&
          Array.isArray(item.tags) &&
          item.tags
            .map((tag) => {
              const tagText =
                typeof tag === "object" && tag.tagName ? tag.tagName : tag;
              return tagText;
            })
            .join(" ")
            .toLowerCase()
            .includes(searchTerm.toLowerCase()));

      // Date range filter
      let matchesDateRange = true;
      if (dateRange && dateRange.length === 2) {
        const itemDate = item.postedAt || item.createdAt;
        if (itemDate) {
          const itemDay = dayjs(itemDate);
          const startDate = dayjs(dateRange[0]).startOf("day");
          const endDate = dayjs(dateRange[1]).endOf("day");
          matchesDateRange = itemDay.isBetween(startDate, endDate, null, "[]");
        } else {
          matchesDateRange = false;
        }
      }

      return matchesSearch && matchesDateRange;
    });

  const getFilteredArticles = () =>
    articles.filter((article) => {
      // Search filter
      const matchesSearch =
        searchTerm === "" ||
        article.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        article.content?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (article.tags &&
          Array.isArray(article.tags) &&
          article.tags
            .map((tag) => {
              const tagText =
                typeof tag === "object" && tag.tagName ? tag.tagName : tag;
              return tagText;
            })
            .join(" ")
            .toLowerCase()
            .includes(searchTerm.toLowerCase()));

      // Date range filter
      let matchesDateRange = true;
      if (dateRange && dateRange.length === 2) {
        const itemDate = article.createdAt;
        if (itemDate) {
          const itemDay = dayjs(itemDate);
          const startDate = dayjs(dateRange[0]).startOf("day");
          const endDate = dayjs(dateRange[1]).endOf("day");
          matchesDateRange = itemDay.isBetween(startDate, endDate, null, "[]");
        } else {
          matchesDateRange = false;
        }
      }

      return matchesSearch && matchesDateRange;
    });

  const handleEditArticle = (article) => {
    const articleUserId = getArticleUserId(article);
    const currentUserId =
      currentUser?.id || currentUser?.userId || currentUser?.userID;

    const canEdit =
      currentUser &&
      (currentUser.role === "4" || // Admin role
        currentUser.role === "admin" || // Fallback for string role
        currentUser.role === "Admin" || // Fallback for string role
        String(articleUserId) === String(currentUserId));

    if (!canEdit) {
      toast.error("Bạn không có quyền chỉnh sửa bài viết này!");
      return;
    }

    setSelectedBlog(article);
    setEditImage(article.imgUrl || null);
    setEditModalVisible(true);
  };

  const handleViewArticle = (article) => {
    setSelectedBlog(article);
    setDetailModalVisible(true);
  };

  const handleDeleteArticle = async (articleId) => {
    const article = findArticleById(articles, articleId);

    if (!article) {
      toast.error("Không tìm thấy bài viết!");
      return;
    }

    const articleUserId = getArticleUserId(article);
    const currentUserId =
      currentUser?.id || currentUser?.userId || currentUser?.userID;

    // Kiểm tra quyền: admin hoặc chính user đó
    const canDelete =
      currentUser &&
      (currentUser.role === "4" || // Admin role
        currentUser.role === "admin" || // Fallback for string role
        currentUser.role === "Admin" || // Fallback for string role
        String(articleUserId) === String(currentUserId));

    if (!canDelete) {
      toast.error("Bạn không có quyền xóa bài viết này!");
      return;
    }

    try {
      // Use the correct ID for API call
      const actualArticleId = getArticleId(article);

      await bloodArticleService.deleteArticle(actualArticleId);

      // Update local state
      setArticles((prev) =>
        prev.filter((a) => {
          const aId = getArticleId(a);
          return aId !== actualArticleId;
        })
      );

      // Refresh articles from server to ensure UI is in sync
      try {
        const refreshedArticles = await bloodArticleService.getBloodArticles();
        setArticles(Array.isArray(refreshedArticles) ? refreshedArticles : []);
      } catch (refreshError) {
        console.error("Error refreshing articles:", refreshError);
      }

      toast.success("Xóa bài viết tài liệu thành công!");
    } catch (error) {
      console.error("Error deleting article:", error);
      toast.error("Có lỗi xảy ra khi xóa bài viết tài liệu!");
    }
  };

  const handleEditNews = (newsItem) => {
    const newsUserId = getNewsUserId(newsItem);
    const currentUserId =
      currentUser?.id || currentUser?.userId || currentUser?.userID;

    const canEdit =
      currentUser &&
      (currentUser.role === "4" || // Admin role
        currentUser.role === "admin" || // Fallback for string role
        String(newsUserId) === String(currentUserId));

    if (!canEdit) {
      toast.error("Bạn không có quyền chỉnh sửa bài viết này!");
      return;
    }

    setSelectedBlog(newsItem);
    setEditImage(newsItem.imgUrl || null);
    setEditModalVisible(true);
  };

  const handleViewNews = (newsItem) => {
    setSelectedBlog(newsItem);
    setDetailModalVisible(true);
  };

  const handleDeleteNews = async (newsId) => {
    const newsItem = findNewsById(news, newsId);

    if (!newsItem) {
      toast.error("Không tìm thấy bài viết!");
      return;
    }

    const newsUserId = getNewsUserId(newsItem);
    const currentUserId =
      currentUser?.id || currentUser?.userId || currentUser?.userID;

    // Kiểm tra quyền: admin hoặc chính user đó
    const canDelete =
      currentUser &&
      (currentUser.role === "4" || // Admin role
        currentUser.role === "admin" || // Fallback for string role
        currentUser.role === "Admin" || // Fallback for string role
        String(newsUserId) === String(currentUserId));

    if (!canDelete) {
      toast.error("Bạn không có quyền xóa bài viết này!");
      return;
    }

    try {
      const actualNewsId = getNewsId(newsItem);
      await newsService.deleteNews(actualNewsId);

      // Update local state
      setNews((prev) =>
        prev.filter((n) => {
          const nId = getNewsId(n);
          return nId !== actualNewsId;
        })
      );

      // Refresh news from server to ensure UI is in sync
      try {
        const refreshedNews = await newsService.fetchAllNews();
        setNews(Array.isArray(refreshedNews) ? refreshedNews : []);
      } catch (refreshError) {
        console.error("Error refreshing news:", refreshError);
      }

      toast.success("Xóa bài viết tin tức thành công!");
    } catch (error) {
      console.error("Error deleting news:", error);
      toast.error("Có lỗi xảy ra khi xóa bài viết tin tức!");
    }
  };

  if (loading) {
    return (
      <DoctorLayout>
        <div className="doctor-content">
          <div className="loading-spinner">
            <div className="spinner"></div>
            <p>Đang tải dữ liệu...</p>
          </div>
        </div>
      </DoctorLayout>
    );
  }

  return (
    <DoctorLayout>
      <PageHeader
        title="Quản lý Blog"
        description="Tạo, chỉnh sửa và quản lý các bài viết tài liệu, tin tức, thông báo của khoa Huyết học"
        icon={FileTextOutlined}
        actions={[
          {
            label: "Làm mới",
            icon: <ReloadOutlined />,
            onClick: () => {
              if (activeTab === "Tài liệu") {
                setArticleLoading(true);
                bloodArticleService
                  .getBloodArticles()
                  .then((articlesData) => {
                    setArticles(
                      Array.isArray(articlesData) ? articlesData : []
                    );
                  })
                  .finally(() => setArticleLoading(false));
              } else if (activeTab === "Tin tức") {
                setNewsLoading(true);
                newsService
                  .fetchAllNews()
                  .then((data) => {
                    setNews(Array.isArray(data) ? data : []);
                  })
                  .finally(() => setNewsLoading(false));
              }
            },
            loading: articleLoading || newsLoading,
          },
        ]}
      />

      <Card style={{ marginBottom: 24 }}>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabCategories.map((tab) => ({
            key: tab.key,
            label: tab.label,
            children: (
              <>
                <Row gutter={[16, 8]} style={{ marginBottom: 16 }}>
                  <Col>
                    <Input.Search
                      placeholder="Tìm kiếm bài viết..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      style={{ width: 260 }}
                      allowClear
                    />
                  </Col>
                  <Col>
                    <Space align="center">
                      <CalendarOutlined style={{ color: "#1890ff" }} />
                      <span style={{ fontSize: "14px", color: "#666" }}>
                        Lọc theo ngày:
                      </span>
                      <RangePicker
                        value={dateRange}
                        onChange={setDateRange}
                        placeholder={["Từ ngày", "Đến ngày"]}
                        format="DD/MM/YYYY"
                        style={{ width: 260 }}
                        presets={[
                          {
                            label: "Hôm nay",
                            value: [dayjs(), dayjs()],
                          },
                          {
                            label: "7 ngày qua",
                            value: [dayjs().subtract(7, "day"), dayjs()],
                          },
                          {
                            label: "30 ngày qua",
                            value: [dayjs().subtract(30, "day"), dayjs()],
                          },
                          {
                            label: "Tháng này",
                            value: [
                              dayjs().startOf("month"),
                              dayjs().endOf("month"),
                            ],
                          },
                          {
                            label: "Tháng trước",
                            value: [
                              dayjs().subtract(1, "month").startOf("month"),
                              dayjs().subtract(1, "month").endOf("month"),
                            ],
                          },
                        ]}
                      />
                      {dateRange && (
                        <Button
                          size="small"
                          onClick={() => setDateRange(null)}
                          type="link"
                          style={{ padding: 0 }}
                        >
                          Xóa lọc ngày
                        </Button>
                      )}
                      {(searchTerm || dateRange) && (
                        <Button
                          size="small"
                          onClick={() => {
                            setSearchTerm("");
                            setDateRange(null);
                          }}
                          style={{ marginLeft: 8 }}
                        >
                          Xóa bộ lọc
                        </Button>
                      )}
                    </Space>
                  </Col>
                  <Col>
                    <Space>
                      {tab.key === "Tài liệu" && (
                        <Button
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={() =>
                            navigate("/doctor/blog/create-article")
                          }
                        >
                          Thêm bài viết mới
                        </Button>
                      )}
                      {tab.key === "Tin tức" && (
                        <Button
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={() => navigate("/doctor/blog/create-news")}
                        >
                          Thêm bài viết mới
                        </Button>
                      )}
                    </Space>
                  </Col>
                </Row>
                {tab.key === "Tài liệu" ? (
                  <Table
                    className="admin-blog-table"
                    columns={BlogTableColumns({
                      activeTab: "Tài liệu",
                      userMap,
                      onView: handleViewArticle,
                      onEdit: handleEditArticle,
                      onDelete: handleDeleteArticle,
                      currentUser,
                    })}
                    dataSource={getFilteredArticles()}
                    rowKey={(record) => getArticleId(record) || record.title}
                    loading={articleLoading}
                    pagination={{ pageSize: 8 }}
                    bordered
                  />
                ) : tab.key === "Tin tức" ? (
                  <Table
                    className="admin-blog-table"
                    columns={BlogTableColumns({
                      activeTab: "Tin tức",
                      userMap,
                      onView: handleViewNews,
                      onEdit: handleEditNews,
                      onDelete: handleDeleteNews,
                      currentUser,
                    })}
                    dataSource={getFilteredNews()}
                    rowKey={(record) => getNewsId(record) || record.title}
                    loading={newsLoading}
                    pagination={{ pageSize: 8 }}
                    bordered
                  />
                ) : null}
              </>
            ),
          }))}
        />
      </Card>
      <BlogEditModal
        visible={editModalVisible}
        selectedBlog={selectedBlog}
        activeTab={activeTab}
        editImage={editImage}
        tags={tags}
        tagsLoading={tagsLoading}
        onCancel={() => setEditModalVisible(false)}
        onSubmit={async () => {
          try {
            const values = await form.validateFields();

            const currentUserId =
              currentUser?.id || currentUser?.userId || currentUser?.userID;

            // Process tags to separate existing tag IDs and new tag names
            const tagsToProcess = values.tags || [];
            const tagIds = [];
            const newTags = [];

            tagsToProcess.forEach((tag) => {
              if (typeof tag === "number" || !isNaN(tag)) {
                // Nếu là number, đó là tagId
                tagIds.push(typeof tag === "number" ? tag : parseInt(tag));
              } else if (typeof tag === "string") {
                // Nếu là string, kiểm tra xem có phải existing tag không
                const existingTag = tags.find(
                  (t) =>
                    (typeof t === "object" && t.tagName === tag) ||
                    (typeof t === "string" && t === tag)
                );

                if (
                  existingTag &&
                  typeof existingTag === "object" &&
                  existingTag.tagId
                ) {
                  // Đây là existing tag, thêm tagId
                  tagIds.push(existingTag.tagId);
                } else {
                  // Đây là new tag
                  newTags.push(tag);
                }
              }
            });

            if (activeTab === "Tài liệu") {
              if (selectedBlog && getArticleId(selectedBlog)) {
                const updateData = {
                  ...values,
                  tagIds: tagIds,
                  newTags: newTags,
                  imgUrl: editImage,
                  userId: currentUserId,
                };
                await bloodArticleService.updateArticle(
                  getArticleId(selectedBlog),
                  updateData
                );
                toast.success("Cập nhật bài viết thành công!");
              } else {
                const createData = {
                  ...values,
                  tagIds: tagIds,
                  newTags: newTags,
                  imgUrl: editImage,
                  userId: currentUserId,
                };
                await bloodArticleService.createArticle(createData);
                toast.success("Tạo bài viết thành công!");
              }
              const articlesData = await bloodArticleService.getBloodArticles();
              setArticles(Array.isArray(articlesData) ? articlesData : []);
            } else if (activeTab === "Tin tức") {
              if (selectedBlog && getNewsId(selectedBlog)) {
                const newsId = getNewsId(selectedBlog);
                const updateData = {
                  ...values,
                  tagIds: tagIds,
                  newTags: newTags,
                  imgUrl: editImage,
                  userId: currentUserId,
                };
                await newsService.updateNews(newsId, updateData);
                toast.success("Cập nhật bài viết tin tức thành công!");
              } else {
                const createData = {
                  ...values,
                  tagIds: tagIds,
                  newTags: newTags,
                  imgUrl: editImage,
                  userId: currentUserId,
                };
                await newsService.createNews(createData);
                toast.success("Tạo bài viết tin tức thành công!");
              }
              const data = await newsService.fetchAllNews();
              setNews(Array.isArray(data) ? data : []);
            }
            setEditModalVisible(false);
          } catch (error) {
            console.error("Error saving:", error);
            toast.error("Có lỗi xảy ra khi lưu bài viết!");
            // Đảm bảo modal đóng ngay cả khi có lỗi
            setEditModalVisible(false);
          }
        }}
        onImageChange={setEditImage}
        form={form}
      />
      <BlogDetailModal
        visible={detailModalVisible}
        selectedBlog={selectedBlog}
        activeTab={activeTab}
        userMap={userMap}
        onClose={() => setDetailModalVisible(false)}
        onDelete={(id) => {
          setDetailModalVisible(false);
          if (activeTab === "Tài liệu") {
            handleDeleteArticle(id);
          } else {
            handleDeleteNews(id);
          }
        }}
      />
    </DoctorLayout>
  );
};

export default BlogManagement;
