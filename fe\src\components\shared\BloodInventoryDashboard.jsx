import React, { useState, useEffect } from "react";
import { Card, Row, Col, Statistic, Radio, Spin, Alert } from "antd";
import { 
  DatabaseOutlined, 
  HeartOutlined, 
  ExclamationCircleOutlined 
} from "@ant-design/icons";
import { fetchBloodInventory } from "../../services/bloodInventoryService";
import BloodInventoryChart from "./BloodInventoryChart";
import BloodInventoryAlertChart from "./BloodInventoryAlertChart";
import "../../styles/components/BloodInventoryDashboard.scss";

// Helper function to extract volume from bagType
const getBagVolume = (bagType) => {
  const match = bagType.match(/(\d+)ml/);
  return match ? parseInt(match[1]) : 0;
};

/**
 * Component dashboard kho máu tóm gọn
 * Bao gồm: Cards tổng quan + 1 biểu đồ chính có thể chuyển đổi
 */
const BloodInventoryDashboard = () => {
  const [overviewData, setOverviewData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [chartView, setChartView] = useState("bloodType"); // "bloodType", "component", "bagType"

  useEffect(() => {
    fetchOverviewData();
  }, []);

  const fetchOverviewData = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetchBloodInventory();
      
      // Calculate overview statistics
      const stats = calculateOverviewStats(response);
      setOverviewData(stats);
    } catch (err) {
      console.error("Error fetching overview data:", err);
      setError("Không thể tải dữ liệu tổng quan");
    } finally {
      setLoading(false);
    }
  };

  const calculateOverviewStats = (apiData) => {
    if (!Array.isArray(apiData)) return null;

    let totalBags = 0;
    let totalVolume = 0;
    const rareBloodTypes = new Set();
    const bloodTypeGroups = new Set();

    apiData.forEach(item => {
      totalBags += item.quantity;
      totalVolume += item.quantity * getBagVolume(item.bagType);
      
      const bloodType = `${item.bloodGroup}${item.rhType}`;
      bloodTypeGroups.add(bloodType);
      
      if (item.isRare) {
        rareBloodTypes.add(bloodType);
      }
    });

    return {
      totalBags,
      totalVolume,
      rareBloodTypesCount: rareBloodTypes.size,
      totalBloodTypes: bloodTypeGroups.size
    };
  };

  const getChartTitle = () => {
    switch (chartView) {
      case "bloodType":
        return "Phân bố theo nhóm máu";
      case "component":
        return "Phân bố theo thành phần máu";
      case "bagType":
        return "Phân bố theo dung tích túi";
      default:
        return "Thống kê kho máu";
    }
  };

  const getChartType = () => {
    return chartView === "bloodType" ? "pie" : "bar";
  };

  if (loading) {
    return (
      <div className="dashboard-loading">
        <Spin size="large" />
        <p>Đang tải dữ liệu kho máu...</p>
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="Lỗi tải dữ liệu"
        description={error}
        type="error"
        showIcon
        action={
          <button onClick={fetchOverviewData} className="retry-button">
            Thử lại
          </button>
        }
      />
    );
  }

  if (!overviewData) return null;

  return (
    <div className="blood-inventory-dashboard">
      {/* Cards tổng quan */}
      <Row gutter={[16, 16]} className="overview-section">
        <Col xs={24} sm={8}>
          <Card className="overview-card total-bags">
            <Statistic
              title="Tổng số túi máu"
              value={overviewData.totalBags}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#1890ff' }}
              suffix="túi"
            />
          </Card>
        </Col>

        <Col xs={24} sm={8}>
          <Card className="overview-card total-volume">
            <Statistic
              title="Tổng thể tích"
              value={overviewData.totalVolume}
              prefix={<HeartOutlined />}
              valueStyle={{ color: '#52c41a' }}
              suffix="ml"
              formatter={(value) => value.toLocaleString()}
            />
          </Card>
        </Col>

        <Col xs={24} sm={8}>
          <Card className="overview-card rare-blood">
            <Statistic
              title="Nhóm máu hiếm"
              value={overviewData.rareBloodTypesCount}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ 
                color: overviewData.rareBloodTypesCount > 0 ? '#faad14' : '#52c41a'
              }}
              suffix="nhóm"
            />
          </Card>
        </Col>
      </Row>

      {/* Biểu đồ chính với filter */}
      <Card className="chart-section">
        <div className="chart-header">
          <h3>Thống kê chi tiết</h3>
          <div className="chart-filters">
            <span className="filter-label">Xem theo:</span>
            <Radio.Group 
              value={chartView} 
              onChange={(e) => setChartView(e.target.value)}
              size="small"
            >
              <Radio.Button value="bloodType">Nhóm máu</Radio.Button>
              <Radio.Button value="component">Thành phần</Radio.Button>
              <Radio.Button value="bagType">Dung tích</Radio.Button>
            </Radio.Group>
          </div>
        </div>

        <BloodInventoryChart
          title={getChartTitle()}
          height={400}
          groupBy={chartView}
          chartType={getChartType()}
          showUnitToggle={true}
        />
      </Card>

      {/* Biểu đồ cảnh báo */}
      <BloodInventoryAlertChart />
    </div>
  );
};

export default BloodInventoryDashboard;
