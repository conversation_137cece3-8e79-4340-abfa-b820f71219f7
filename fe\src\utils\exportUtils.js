import * as XLSX from 'xlsx';
import jsPDF from 'jspdf';
import 'jspdf-autotable';


export const exportToExcel = (data, filename = 'report') => {
  try {
    // Create a new workbook
    const workbook = XLSX.utils.book_new();

    // Overview sheet
    const overviewData = [
      ['<PERSON><PERSON><PERSON> cáo thống kê hệ thống'],
      ['<PERSON><PERSON><PERSON> tạo:', new Date(data.generatedAt).toLocaleString('vi-VN')],
      [''],
      ['TỔNG QUAN HỆ THỐNG'],
      ['Tổng số người dùng:', data.overview.totalUsers],
      ['Tổng số yêu cầu máu:', data.overview.totalBloodRequests],
      ['Tổng số bài viết:', data.overview.totalArticles],
      ['Tổng số tin tức:', data.overview.totalNews],
      [''],
      ['THỐNG KÊ NGƯỜI DÙNG'],
      ['Tổng số:', data.users.total],
      ['<PERSON><PERSON> hoạt động:', data.users.active],
      ['<PERSON><PERSON><PERSON>ng hoạt động:', data.users.inactive],
      [''],
      ['<PERSON> v<PERSON> tr<PERSON>:'],
      ['- <PERSON><PERSON><PERSON><PERSON> trị viên:', data.users.byRole.admin],
      ['- Quản lý:', data.users.byRole.manager],
      ['- Bác sĩ:', data.users.byRole.doctor],
      ['- Thành viên:', data.users.byRole.member],
      [''],
      ['Theo giới tính:'],
      ['- Nam:', data.users.byGender.male],
      ['- Nữ:', data.users.byGender.female],
      ['- Khác:', data.users.byGender.other],
      [''],
      ['THỐNG KÊ YÊU CẦU MÁU'],
      ['Tổng số:', data.bloodRequests.total],
      ['Chờ duyệt:', data.bloodRequests.byStatus.pending],
      ['Đã duyệt:', data.bloodRequests.byStatus.accepted],
      ['Hoàn thành:', data.bloodRequests.byStatus.completed],
      ['Từ chối:', data.bloodRequests.byStatus.rejected],
      ['Tổng lượng máu yêu cầu (ml):', data.bloodRequests.totalQuantity]
    ];

    const overviewSheet = XLSX.utils.aoa_to_sheet(overviewData);
    XLSX.utils.book_append_sheet(workbook, overviewSheet, 'Tổng quan');

    // Users by blood type sheet
    if (data.users.byBloodType && Object.keys(data.users.byBloodType).length > 0) {
      const bloodTypeData = [
        ['Nhóm máu', 'Số lượng người dùng'],
        ...Object.entries(data.users.byBloodType).map(([type, count]) => [type, count])
      ];
      const bloodTypeSheet = XLSX.utils.aoa_to_sheet(bloodTypeData);
      XLSX.utils.book_append_sheet(workbook, bloodTypeSheet, 'Người dùng theo nhóm máu');
    }

    // Blood requests by blood type sheet
    if (data.bloodRequests.byBloodType && Object.keys(data.bloodRequests.byBloodType).length > 0) {
      const requestBloodTypeData = [
        ['Nhóm máu', 'Số lượng yêu cầu'],
        ...Object.entries(data.bloodRequests.byBloodType).map(([type, count]) => [type, count])
      ];
      const requestBloodTypeSheet = XLSX.utils.aoa_to_sheet(requestBloodTypeData);
      XLSX.utils.book_append_sheet(workbook, requestBloodTypeSheet, 'Yêu cầu theo nhóm máu');
    }

    // Blood requests by month sheet
    if (data.bloodRequests.byMonth && Object.keys(data.bloodRequests.byMonth).length > 0) {
      const monthlyData = [
        ['Tháng', 'Số lượng yêu cầu'],
        ...Object.entries(data.bloodRequests.byMonth)
          .sort(([a], [b]) => new Date(a) - new Date(b))
          .map(([month, count]) => [month, count])
      ];
      const monthlySheet = XLSX.utils.aoa_to_sheet(monthlyData);
      XLSX.utils.book_append_sheet(workbook, monthlySheet, 'Yêu cầu theo tháng');
    }

    // Department statistics (if available)
    if (data.users.byDepartment && Object.keys(data.users.byDepartment).length > 0) {
      const departmentData = [
        ['Khoa', 'Số lượng bác sĩ'],
        ...Object.entries(data.users.byDepartment).map(([dept, count]) => [dept, count])
      ];
      const departmentSheet = XLSX.utils.aoa_to_sheet(departmentData);
      XLSX.utils.book_append_sheet(workbook, departmentSheet, 'Bác sĩ theo khoa');
    }

    // Write the file
    const fileName = `${filename}_${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(workbook, fileName);

    return { success: true, fileName };
  } catch (error) {
    console.error('Error exporting to Excel:', error);
    return { success: false, error: error.message };
  }
};


export const exportToPDF = (data, filename = 'report') => {
  try {
    // Create new PDF document
    const doc = new jsPDF();
    
    // Set font for Vietnamese text
    doc.setFont('helvetica');
    
    // Title
    doc.setFontSize(20);
    doc.text('BÁO CÁO THỐNG KÊ HỆ THỐNG', 20, 20);
    
    // Generated date
    doc.setFontSize(12);
    doc.text(`Ngày tạo: ${new Date(data.generatedAt).toLocaleString('vi-VN')}`, 20, 35);
    
    let yPosition = 50;

    // Overview section
    doc.setFontSize(16);
    doc.text('TỔNG QUAN HỆ THỐNG', 20, yPosition);
    yPosition += 15;

    doc.setFontSize(12);
    const overviewData = [
      ['Chỉ số', 'Giá trị'],
      ['Tổng số người dùng', data.overview.totalUsers.toString()],
      ['Tổng số yêu cầu máu', data.overview.totalBloodRequests.toString()],
      ['Tổng số bài viết', data.overview.totalArticles.toString()],
      ['Tổng số tin tức', data.overview.totalNews.toString()]
    ];

    doc.autoTable({
      head: [overviewData[0]],
      body: overviewData.slice(1),
      startY: yPosition,
      theme: 'grid',
      headStyles: { fillColor: [41, 128, 185] },
      styles: { fontSize: 10 }
    });

    yPosition = doc.lastAutoTable.finalY + 20;

    // User statistics section
    doc.setFontSize(16);
    doc.text('THỐNG KÊ NGƯỜI DÙNG', 20, yPosition);
    yPosition += 15;

    const userStatsData = [
      ['Thông tin', 'Số lượng'],
      ['Tổng số người dùng', data.users.total.toString()],
      ['Đang hoạt động', data.users.active.toString()],
      ['Không hoạt động', data.users.inactive.toString()],
      ['Quản trị viên', data.users.byRole.admin.toString()],
      ['Quản lý', data.users.byRole.manager.toString()],
      ['Bác sĩ', data.users.byRole.doctor.toString()],
      ['Thành viên', data.users.byRole.member.toString()]
    ];

    doc.autoTable({
      head: [userStatsData[0]],
      body: userStatsData.slice(1),
      startY: yPosition,
      theme: 'grid',
      headStyles: { fillColor: [52, 152, 219] },
      styles: { fontSize: 10 }
    });

    yPosition = doc.lastAutoTable.finalY + 20;

    // Blood request statistics section
    if (yPosition > 250) {
      doc.addPage();
      yPosition = 20;
    }

    doc.setFontSize(16);
    doc.text('THỐNG KÊ YÊU CẦU MÁU', 20, yPosition);
    yPosition += 15;

    const bloodRequestData = [
      ['Trạng thái', 'Số lượng'],
      ['Tổng số yêu cầu', data.bloodRequests.total.toString()],
      ['Chờ duyệt', data.bloodRequests.byStatus.pending.toString()],
      ['Đã duyệt', data.bloodRequests.byStatus.accepted.toString()],
      ['Hoàn thành', data.bloodRequests.byStatus.completed.toString()],
      ['Từ chối', data.bloodRequests.byStatus.rejected.toString()],
      ['Tổng lượng máu (ml)', data.bloodRequests.totalQuantity.toString()]
    ];

    doc.autoTable({
      head: [bloodRequestData[0]],
      body: bloodRequestData.slice(1),
      startY: yPosition,
      theme: 'grid',
      headStyles: { fillColor: [231, 76, 60] },
      styles: { fontSize: 10 }
    });

    // Blood type statistics (if available)
    if (data.users.byBloodType && Object.keys(data.users.byBloodType).length > 0) {
      yPosition = doc.lastAutoTable.finalY + 20;
      
      if (yPosition > 250) {
        doc.addPage();
        yPosition = 20;
      }

      doc.setFontSize(16);
      doc.text('THỐNG KÊ THEO NHÓM MÁU', 20, yPosition);
      yPosition += 15;

      const bloodTypeTableData = [
        ['Nhóm máu', 'Số người dùng', 'Số yêu cầu'],
        ...Object.keys(data.users.byBloodType).map(type => [
          type,
          (data.users.byBloodType[type] || 0).toString(),
          (data.bloodRequests.byBloodType[type] || 0).toString()
        ])
      ];

      doc.autoTable({
        head: [bloodTypeTableData[0]],
        body: bloodTypeTableData.slice(1),
        startY: yPosition,
        theme: 'grid',
        headStyles: { fillColor: [155, 89, 182] },
        styles: { fontSize: 10 }
      });
    }

    // Save the PDF
    const fileName = `${filename}_${new Date().toISOString().split('T')[0]}.pdf`;
    doc.save(fileName);

    return { success: true, fileName };
  } catch (error) {
    console.error('Error exporting to PDF:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Format number with thousand separators
 * @param {number} num - Number to format
 * @returns {string} Formatted number
 */
export const formatNumber = (num) => {
  return new Intl.NumberFormat('vi-VN').format(num);
};

/**
 * Format date to Vietnamese locale
 * @param {string|Date} date - Date to format
 * @returns {string} Formatted date
 */
export const formatDate = (date) => {
  return new Date(date).toLocaleDateString('vi-VN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};
