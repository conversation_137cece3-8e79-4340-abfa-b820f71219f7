import React, { useState, useEffect } from "react";
import AdminLayout from "../../components/admin/AdminLayout";
import { toast } from "../../utils/toastUtils";
import ProfileUpdateForm from "../../components/shared/ProfileUpdateForm";
import { getUserInfo } from "../../services/informationService";
import authService from "../../services/authService";
import "../../styles/pages/ProfilePage.scss";

const AdminProfilePage = () => {
  const [userInfo, setUserInfo] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchUserInfo();
  }, []);

  const fetchUserInfo = async () => {
    try {
      setLoading(true);
      const allUsers = await getUserInfo(); // This returns an array of all users

      // Get current user ID from auth service
      const currentUser = authService.getCurrentUser();
      let userId = currentUser?.id;

      // Also try to get userId from JWT token as fallback
      const token = localStorage.getItem("authToken");
      if (token && (!userId || userId === "0" || userId === 0)) {
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          const jwtUserId = payload["http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier"];
          if (jwtUserId && jwtUserId !== "0") {
            userId = jwtUserId;
          }
        } catch (e) {
          console.error("Error parsing JWT:", e);
        }
      }

      console.log("Current user ID:", userId);
      console.log("All users from API:", allUsers);

      // Find current user in the array
      const currentUserData = Array.isArray(allUsers)
        ? allUsers.find(user => user.userID === parseInt(userId))
        : allUsers;

      console.log("Found current user data:", currentUserData);

      if (currentUserData) {
        setUserInfo(currentUserData);
      } else {
        toast.error("Không tìm thấy thông tin người dùng hiện tại!");
      }
    } catch (error) {
      console.error("Error fetching user info:", error);
      toast.error("Không thể tải thông tin người dùng!");
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateSuccess = (updatedInfo) => {
    setUserInfo(prev => ({ ...prev, ...updatedInfo }));
  };

  return (
    <AdminLayout>
      <div className="profile-page">
        <ProfileUpdateForm
          userInfo={userInfo}
          onUpdateSuccess={handleUpdateSuccess}
          title="Cập nhật hồ sơ quản trị viên"
        />
      </div>
    </AdminLayout>
  );
};

export default AdminProfilePage;
