import { useState, useEffect, useCallback } from "react";
import userInfoService from "../services/userInfoService";
import { getDepartmentNameById, getAllDepartments } from "../utils/departmentUtils";
import { toast } from "../utils/toastUtils";

const ROLE_MAP = {
  1: { value: "member", label: "Thành viên" },
  2: { value: "doctor", label: "<PERSON>ác sĩ" },
  3: { value: "manager", label: "Quản lý" },
  4: { value: "admin", label: "Quản trị viên" },
};

const STATUS_OPTIONS = [
  { value: "active", label: "Hoạt động", apiValue: 1 },
  { value: "inactive", label: "Đình chỉ hoạt động", apiValue: 0 },
];

// Get departments from utils
const DEPARTMENTS = getAllDepartments().map(dept => dept.name);

export const useUserManagement = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [deleteUserId, setDeleteUserId] = useState(null);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);

  const mapUserData = (userData) => {
    return (Array.isArray(userData) ? userData : []).map((u) => {
      const roleObj = ROLE_MAP[u.roleID] || ROLE_MAP[1];
      let role = roleObj.value;
      let roleLabel = roleObj.label;
      if (role === "doctor") {
        // Use departmentId for more reliable mapping
        const departmentName =
          getDepartmentNameById(u.departmentId) !== "Không xác định"
            ? getDepartmentNameById(u.departmentId)
            : u.department;
        if (
          u.departmentId === 1 ||
          (u.department && u.department.trim() === "Khoa Huyết học")
        ) {
          role = "doctor_blood";
          roleLabel = "Bác sĩ - Khoa Huyết học";
        } else if (departmentName) {
          role = "doctor_other";
          roleLabel = `Bác sĩ - ${departmentName}`;
        } else {
          role = "doctor_other";
          roleLabel = "Bác sĩ";
        }
      }
      // Handle both numeric and string status values from API
      const userStatus = Number(u.status); // Convert to number for comparison
      const statusObj =
        STATUS_OPTIONS.find((s) => s.apiValue === userStatus) ||
        STATUS_OPTIONS[0]; // Default to "active" if status not found
      return {
        id: u.userID,
        userID: u.userID,
        name: u.fullName || u.name || "",
        email: u.email || "",
        phone: u.phoneNumber || u.phone || "",
        role,
        roleLabel,
        roleID: u.roleID, // Add roleID for proper role checking
        status: statusObj.value,
        statusLabel: statusObj.label,
        bloodType: u.bloodGroup || u.bloodType || "",
        department: u.department || "",
        departmentId: u.departmentId || u.departmentID || null, // Add departmentId mapping
        createdAt: u.createdAt || "",
        // Add all fields needed for API update
        idCardType: u.idCardType || "",
        idCard: u.idCard || "",
        dateOfBirth: u.dateOfBirth || null,
        age: u.age || null,
        gender: u.gender || null,
        city: u.city || "",
        district: u.district || "",
        ward: u.ward || "",
        address: u.address || "",
        distance: u.distance || null,
        bloodGroup: u.bloodGroup || "",
        rhType: u.rhType || "",
        weight: u.weight || null,
        height: u.height || null,
      };
    });
  };

  const loadUsers = useCallback(async () => {
    setLoading(true);
    try {
      const data = await userInfoService.getAllUsers();
      const mapped = mapUserData(data);

      // Get cached users from localStorage (including suspended ones)
      const cachedUsers = JSON.parse(localStorage.getItem("allUsers") || "[]");

      // Merge API data with cached data
      const mergedUsers = [...mapped];

      // Add any cached suspended users that are not in API response
      cachedUsers.forEach((cachedUser) => {
        const existsInAPI = mapped.find((u) => u.userID === cachedUser.userID);
        if (!existsInAPI && cachedUser.status === "inactive") {
          // Add suspended user back to list
          mergedUsers.push(cachedUser);
        }
      });

      // Update cache with current data
      localStorage.setItem("allUsers", JSON.stringify(mergedUsers));

      setUsers(mergedUsers);
    } catch {
      toast.error("Không thể tải dữ liệu người dùng từ API!");
    } finally {
      setLoading(false);
    }
  }, []);

  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesSearch;
  });

  const handleEditUser = (user) => {
    setEditingUser(user);
    setShowModal(true);
  };

  const handleCreateUser = () => {
    setEditingUser(null);
    setShowModal(true);
  };

  const handleDeleteUser = async () => {
    if (!deleteUserId) return;
    setDeleteModalVisible(false);
    setDeleteUserId(null);
    try {
      setLoading(true);
      await userInfoService.deleteUser(deleteUserId);
      await loadUsers();
      toast.success("Đã xóa người dùng!");
    } catch {
      toast.error("Lỗi khi xóa người dùng!");
    } finally {
      setLoading(false);
    }
  };

  const handleModalOk = async (form, values) => {
    if (editingUser) {
      try {
        setLoading(true);
        const statusObj =
          STATUS_OPTIONS.find((s) => s.value === values.status) ||
          STATUS_OPTIONS[1];
        const userData = {
          userID: editingUser.userID || editingUser.id,
          email: values.email || "",
          password: editingUser.password || "Ab1234@",
          phone: values.phone || "",
          idCardType: editingUser.idCardType || "",
          idCard: editingUser.idCard || "",
          name: values.name || "",
          dateOfBirth: editingUser.dateOfBirth || null,
          age: editingUser.age || null,
          gender: values.gender || editingUser.gender || "",
          city: editingUser.city || "",
          district: editingUser.district || "",
          ward: editingUser.ward || "",
          address: editingUser.address || "",
          distance: editingUser.distance || null,
          bloodGroup: values.bloodType || editingUser.bloodType || "",
          rhType: values.rhType || editingUser.rhType || "",
          weight: editingUser.weight || null,
          height: editingUser.height || null,
          status: statusObj.apiValue,
          roleID: Number(values.roleID),
          departmentId:
            Number(values.roleID) === 2
              ? Number(values.departmentId) || editingUser.departmentId || null
              : null,
          createdAt: editingUser.createdAt || new Date().toISOString(),
        };
        await userInfoService.updateUserInfo(editingUser.id, userData);
        setShowModal(false);
        setEditingUser(null);
        form.resetFields();
        toast.success("Cập nhật thành công!");
        await loadUsers();
      } catch (error) {
        // Show specific validation errors if available
        if (error.response?.data?.errors) {
          const errorMessages = Object.entries(error.response.data.errors)
            .map(
              ([field, messages]) =>
                `${field}: ${
                  Array.isArray(messages) ? messages.join(", ") : messages
                }`
            )
            .join("\n");
          toast.error(`Validation errors:\n${errorMessages}`);
        } else {
          toast.error("Lỗi khi cập nhật người dùng!");
        }
      } finally {
        setLoading(false);
      }
    } else {
      try {
        setLoading(true);
        const statusObj =
          STATUS_OPTIONS.find((s) => s.value === values.status) ||
          STATUS_OPTIONS[1];
        const userData = {
          name: values.name,
          email: values.email,
          phone: values.phone,
          roleID: Number(values.roleID),
          status: statusObj.apiValue,
          bloodGroup: values.bloodType || "",
          // Handle department information for doctors
          departmentId:
            Number(values.roleID) === 2
              ? Number(values.departmentId) || null
              : null,
          password: "Ab1234@",
          city: "",
          ward: "",
          gender: "",
          idCard: "",
          rhType: "",
          address: "",
          district: "",
          idCardType: "",
        };
        await userInfoService.createUser(userData);
        toast.success("Thêm mới thành công!");
        await loadUsers();
        setShowModal(false);
        setEditingUser(null);
      } catch {
        toast.error("Lỗi khi thêm người dùng!");
      } finally {
        setLoading(false);
      }
    }
  };

  const handleDeleteClick = (user) => {
    setDeleteUserId(user.id);
    setDeleteModalVisible(true);
  };

  const handleModalCancel = () => {
    setShowModal(false);
    setEditingUser(null);
  };

  // Force reload all users (including suspended ones from cache/state)
  const forceReloadUsers = () => {
    loadUsers();
    toast.info("Đã tải lại danh sách người dùng từ API");
  };

  // Handle status change - simplified and more robust
  const handleStatusChange = async (userId, newStatus) => {
    try {
      setLoading(true);

      const statusObj =
        STATUS_OPTIONS.find((s) => s.value === newStatus) || STATUS_OPTIONS[0];

      // Find the current user to get all required fields
      const currentUser = users.find((u) => u.userID === userId);
      if (!currentUser) {
        throw new Error("Không tìm thấy thông tin người dùng");
      }

      // Include all required fields based on API requirements
      const userData = {
        userID: userId,
        name: currentUser.name || currentUser.fullName || "",
        email: currentUser.email || "",
        phone: currentUser.phone || currentUser.phoneNumber || "",
        roleID: Number(currentUser.roleID),
        status: statusObj.apiValue, // 0 for inactive, 1 for active
        bloodGroup: currentUser.bloodGroup || currentUser.bloodType || "",
        password: "Ab1234@", // Required field - use default password for status updates
        // Location fields
        city: currentUser.city || "",
        ward: currentUser.ward || "",
        district: currentUser.district || "",
        address: currentUser.address || "",
        // Personal info fields
        gender: currentUser.gender || "",
        idCard: currentUser.idCard || "",
        idCardType: currentUser.idCardType || "",
        rhType: currentUser.rhType || "",
        dateOfBirth: currentUser.dateOfBirth || null,
        age: currentUser.age || null,
        weight: currentUser.weight || null,
        height: currentUser.height || null,
        distance: currentUser.distance || null,
        // Department info (only for doctors)
        departmentId: Number(currentUser.roleID) === 2 ? (Number(currentUser.departmentId) || null) : null,
        department: Number(currentUser.roleID) === 2 ? (currentUser.department || "") : "",
        // Timestamps
        createdAt: currentUser.createdAt || new Date().toISOString(),
      };

      // Update user status via API
      await userInfoService.updateUser(userData);



      // Update local state
      const updatedUsers = users.map((user) =>
        user.userID === userId
          ? {
              ...user,
              status: statusObj.value,
              statusLabel: statusObj.label,
            }
          : user
      );

      setUsers(updatedUsers);

      // Update cache to persist suspended users
      localStorage.setItem("allUsers", JSON.stringify(updatedUsers));

      // Show appropriate success message
      if (statusObj.apiValue === 0) {
        toast.success(
          `Đã đình chỉ hoạt động của "${currentUser.name}". Người dùng sẽ không thể đăng nhập và bị đăng xuất nếu đang online.`
        );
      } else {
        toast.success(`Đã kích hoạt lại người dùng "${currentUser.name}"`);
      }
    } catch (error) {

      // Enhanced error handling with more specific messages
      if (error.response?.status === 401) {
        toast.error("Không có quyền thực hiện thao tác này. Vui lòng đăng nhập lại.");
      } else if (error.response?.status === 403) {
        toast.error("Bạn không có quyền thay đổi trạng thái người dùng này.");
      } else if (error.response?.status === 404) {
        toast.error("Không tìm thấy người dùng cần cập nhật.");
      } else if (error.response?.status === 400) {
        // Handle validation errors
        if (error.response?.data?.errors) {
          const errorMessages = Object.entries(error.response.data.errors)
            .map(
              ([field, messages]) =>
                `${field}: ${
                  Array.isArray(messages) ? messages.join(", ") : messages
                }`
            )
            .join("\n");
          toast.error(`Lỗi validation:\n${errorMessages}`);
        } else if (error.response?.data?.message) {
          toast.error(`Lỗi dữ liệu: ${error.response.data.message}`);
        } else {
          toast.error("Dữ liệu gửi lên không hợp lệ.");
        }
      } else if (error.response?.status >= 500) {
        toast.error("Lỗi server. Vui lòng thử lại sau hoặc liên hệ quản trị viên.");
      } else if (error.response?.data?.message) {
        toast.error(`Lỗi từ server: ${error.response.data.message}`);
      } else if (error.message) {
        toast.error(`Lỗi: ${error.message}`);
      } else {
        toast.error("Lỗi không xác định khi cập nhật trạng thái người dùng!");
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadUsers();
  }, [loadUsers]);

  return {
    users: filteredUsers,
    loading,
    searchTerm,
    showModal,
    editingUser,
    deleteModalVisible,

    // Constants
    STATUS_OPTIONS,
    DEPARTMENTS,

    setSearchTerm,
    setDeleteModalVisible,

    handleEditUser,
    handleCreateUser,
    handleDeleteUser,
    handleModalOk,
    handleDeleteClick,
    handleModalCancel,
    handleStatusChange,
    forceReloadUsers,
  };
};
