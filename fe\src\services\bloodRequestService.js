import axiosInstance from "./axiosInstance";
import config from "../config/environment";
import {
  prepareBloodRequestUpdateData,
  validateBloodRequestData,
  logApiRequest,
  logApiResponse,
} from "../utils/bloodRequestApiUtils";
import bloodRequestNotificationService from "./bloodRequestNotificationService";
import authService from "./authService";

const BLOOD_REQUEST_API = config.api.bloodRequest;


export const bloodRequestService = {
  
  createBloodRequest: async (requestData) => {
    try {
      const response = await axiosInstance.post(BLOOD_REQUEST_API, requestData);

      return {
        success: true,
        data: response.data,
        message: "Yêu cầu máu đã được tạo thành công",
      };
    } catch (error) {
      console.error("📤 Request data sent:", requestData);

      // Return error for all types of errors (no mock fallback)
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.title ||
          error.message ||
          "C<PERSON> lỗi xảy ra khi tạo yêu cầu máu",
        details: error.response?.data,
        status: error.response?.status,
      };
    }
  },

  
  createBloodRequestWithFile: async (requestData, pdfFile) => {
    try {
      const formData = new FormData();

      // Append all fields to FormData
      Object.keys(requestData).forEach((key) => {
        if (requestData[key] !== null && requestData[key] !== undefined) {
          formData.append(key, requestData[key]);
        }
      });

      // Append PDF file as MedicalFile (binary data that backend expects)
      if (pdfFile) {
        formData.append("MedicalFile", pdfFile);
      }

      const response = await axiosInstance.post(BLOOD_REQUEST_API, formData);

      return {
        success: true,
        data: response.data,
        message: "Yêu cầu máu đã được tạo thành công",
      };
    } catch (error) {
      console.error("📤 FormData request error:", error);

      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.title ||
          error.message ||
          "Có lỗi xảy ra khi tạo yêu cầu máu",
        details: error.response?.data,
        status: error.response?.status,
      };
    }
  },

  
  getBloodRequests: async (params = {}) => {
    try {
      const response = await axiosInstance.get(BLOOD_REQUEST_API, { params });
      return {
        success: true,
        data: response.data,
        message: "Lấy danh sách yêu cầu máu thành công",
      };
    } catch (error) {
      console.error("Error fetching blood requests:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          "Có lỗi xảy ra khi lấy danh sách yêu cầu máu",
        details: error.response?.data,
      };
    }
  },

  getAllBloodRequests: async (params = {}) => {
    return bloodRequestService.getBloodRequests(params);
  },

  getBloodRequestById: async (id) => {
    try {
      const response = await axiosInstance.get(`${BLOOD_REQUEST_API}/${id}`);

      return {
        success: true,
        data: response.data,
        message: "Lấy chi tiết yêu cầu máu thành công",
      };
    } catch (error) {
      console.error("Error fetching blood request by ID:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          "Có lỗi xảy ra khi lấy chi tiết yêu cầu máu",
        details: error.response?.data,
      };
    }
  },

  
  updateBloodRequest: async (id, updateData) => {
    try {
      const response = await axiosInstance.get(`${BLOOD_REQUEST_API}/${id}`);
      return {
        success: true,
        data: response.data,
        message: "Lấy thông tin yêu cầu máu thành công",
      };
    } catch (error) {
      console.error("Error fetching blood request:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          "Có lỗi xảy ra khi lấy thông tin yêu cầu máu",
        details: error.response?.data,
      };
    }
  },

  updateBloodRequest: async (id, updateData) => {
    try {
      const response = await axiosInstance.put(
        `${BLOOD_REQUEST_API}/${id}`,
        updateData
      );
      return {
        success: true,
        data: response.data,
        message: "Cập nhật yêu cầu máu thành công",
      };
    } catch (error) {
      console.error("Error updating blood request:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          "Có lỗi xảy ra khi cập nhật yêu cầu máu",
        details: error.response?.data,
      };
    }
  },

  deleteBloodRequest: async (id) => {
    try {
      const response = await axiosInstance.delete(`${BLOOD_REQUEST_API}/${id}`);
      return {
        success: true,
        data: response.data,
        message: "Xóa yêu cầu máu thành công",
      };
    } catch (error) {
      console.error("Error deleting blood request:", error);
      return {
        success: false,
        error:
          error.response?.data?.message || "Có lỗi xảy ra khi xóa yêu cầu máu",
        details: error.response?.data,
      };
    }
  },

  
  checkUserPendingRequest: async (userId) => {
    try {
      // Get all blood requests first
      const response = await axiosInstance.get(BLOOD_REQUEST_API);

      // Filter by userID and pending status (status = 0)
      const pendingRequests = response.data.filter(
        (request) =>
          (request.userID === parseInt(userId) ||
            request.userId === parseInt(userId)) &&
          request.status === 0 // Pending status
      );

      return {
        success: true,
        hasPendingRequest: pendingRequests.length > 0,
        pendingRequest: pendingRequests.length > 0 ? pendingRequests[0] : null,
        message:
          pendingRequests.length > 0
            ? "Bạn đã có đơn đăng ký nhận máu đang chờ xử lý"
            : "Có thể tạo đơn đăng ký mới",
      };
    } catch (error) {
      console.error("❌ Error checking pending blood requests:", error);

      return {
        success: false,
        hasPendingRequest: false,
        error:
          error.response?.data?.message ||
          "Có lỗi xảy ra khi kiểm tra trạng thái đơn đăng ký",
        details: error.response?.data,
      };
    }
  },

  getBloodRequestsByUser: async (userId) => {
    try {
      // Get all blood requests first
      const response = await axiosInstance.get(BLOOD_REQUEST_API);

      // Filter by userID on frontend
      const userBloodRequests = response.data.filter((request) => {
        return (
          request.userID === parseInt(userId) ||
          request.userId === parseInt(userId)
        );
      });

      return {
        success: true,
        data: userBloodRequests,
        message: "Lấy danh sách yêu cầu máu của người dùng thành công",
      };
    } catch (error) {
      return {
        success: false,
        error:
          error.response?.data?.message ||
          "Có lỗi xảy ra khi lấy danh sách yêu cầu máu",
        details: error.response?.data,
      };
    }
  },

  getBloodRequestsByDoctor: async (doctorId) => {
    try {
      // Get all blood requests first
      const response = await axiosInstance.get(BLOOD_REQUEST_API);

      // Filter by doctorId on frontend
      const doctorBloodRequests = response.data.filter((request) => {
        return (
          request.doctorId === parseInt(doctorId) ||
          request.doctorID === parseInt(doctorId) ||
          request.userID === parseInt(doctorId) // In case doctor creates requests
        );
      });

      return {
        success: true,
        data: doctorBloodRequests,
        message: "Lấy danh sách yêu cầu máu của bác sĩ thành công",
      };
    } catch (error) {
      return {
        success: false,
        error:
          error.response?.data?.message ||
          "Có lỗi xảy ra khi lấy danh sách yêu cầu máu của bác sĩ",
        details: error.response?.data,
      };
    }
  },

  updateBloodRequestStatus: async (id, status, requestData = null) => {
    try {
      const response = await axiosInstance.patch(
        `${BLOOD_REQUEST_API}/${id}/status`,
        { status }
      );

      let responseData = response.data;
      if (!responseData || responseData === '' || (typeof responseData === 'string' && responseData.trim() === '')) {
        responseData = { success: true, requestId: id, status: status };
      }

      return {
        success: true,
        data: responseData,
        message: "Cập nhật trạng thái yêu cầu máu thành công",
      };
    } catch (error) {
      return {
        success: false,
        error:
          error.response?.data?.message ||
          "Có lỗi xảy ra khi cập nhật trạng thái",
        details: error.response?.data,
      };
    }
  },

 
  updateBloodRequestStatusWithReason: async (id, status, reason) => {
    try {
      const requestData = {
        status,
        note: reason
      };

      const response = await axiosInstance.patch(
        `${BLOOD_REQUEST_API}/${id}/status`,
        requestData
      );

      return {
        success: true,
        data: response.data,
        message: "Cập nhật trạng thái yêu cầu máu thành công",
      };
    } catch (error) {
      return {
        success: false,
        error:
          error.response?.data?.message ||
          "Có lỗi xảy ra khi cập nhật trạng thái",
        details: error.response?.data,
      };
    }
  },

  rejectBloodRequest: async (id, reason, requestData) => {
    try {
      const statusResponse = await bloodRequestService.updateBloodRequestStatusWithReason(
        id,
        3,
        reason
      );

      return statusResponse;

    } catch (error) {
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.title ||
          "Có lỗi xảy ra khi từ chối yêu cầu máu",
        details: error.response?.data,
      };
    }
  },

 
  acceptBloodRequest: async (id, requestData) => {
    try {
      // Get current request data first if not provided
      let currentData = requestData;
      if (!currentData) {
        const getResponse = await axiosInstance.get(
          `${BLOOD_REQUEST_API}/${id}`
        );
        currentData = getResponse.data;
      }

      // Strategy 1: Try simple status update first (like blood donation)
      try {
        const statusResponse =
          await bloodRequestService.updateBloodRequestStatus(id, 1, currentData);

        if (statusResponse.success) {
          return statusResponse;
        }
      } catch (statusError) {
        // Continue to Strategy 2
      }

      // Strategy 2: Fallback to full object PUT (original method)

      // Prepare and validate data
      const updateData = prepareBloodRequestUpdateData(currentData, 1);

      // Handle patientId foreign key constraint
      if (updateData.patientId && updateData.patientId !== 0) {
        updateData.patientId = null;
      }

      const validation = validateBloodRequestData(updateData);

      if (!validation.isValid) {
        return {
          success: false,
          error: "Dữ liệu không hợp lệ: " + validation.errors.join(", "),
          details: { errors: validation.errors },
        };
      }

      const response = await axiosInstance.put(
        `${BLOOD_REQUEST_API}/${id}`,
        updateData
      );

      return {
        success: true,
        data: response.data,
        message: "Chấp nhận yêu cầu máu thành công",
      };
    } catch (error) {
      // Handle specific FK constraint error
      if (error.response?.data?.message?.includes("FOREIGN KEY constraint")) {
        return {
          success: false,
          error:
            "Thông tin bệnh nhân không hợp lệ. Vui lòng kiểm tra lại mã bệnh nhân.",
          details: error.response?.data,
        };
      }

      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.title ||
          "Có lỗi xảy ra khi chấp nhận yêu cầu máu",
        details: error.response?.data,
      };
    }
  },

  
  completeBloodRequest: async (id, requestData) => {
    try {
      // Get current request data first if not provided
      let currentData = requestData;
      if (!currentData) {
        const getResponse = await axiosInstance.get(
          `${BLOOD_REQUEST_API}/${id}`
        );
        currentData = getResponse.data;
      }

      // Strategy 1: Try simple status update first (like blood donation)
      try {
        const statusResponse =
          await bloodRequestService.updateBloodRequestStatus(id, 2, currentData);

        if (statusResponse.success) {
          return statusResponse;
        }
      } catch (statusError) {
        // Continue to Strategy 2
      }

      // Strategy 2: Fallback to full object PUT (original method)

      // Prepare and validate data
      const updateData = prepareBloodRequestUpdateData(currentData, 2);

      // Handle patientId foreign key constraint
      if (updateData.patientId && updateData.patientId !== 0) {
        updateData.patientId = null;
      }

      const validation = validateBloodRequestData(updateData);

      if (!validation.isValid) {
        return {
          success: false,
          error: "Dữ liệu không hợp lệ: " + validation.errors.join(", "),
          details: { errors: validation.errors },
        };
      }

      const response = await axiosInstance.put(
        `${BLOOD_REQUEST_API}/${id}`,
        updateData
      );

      return {
        success: true,
        data: response.data,
        message: "Hoàn thành yêu cầu máu thành công",
      };
    } catch (error) {
      // Handle specific FK constraint error
      if (error.response?.data?.message?.includes("FOREIGN KEY constraint")) {
        return {
          success: false,
          error:
            "Thông tin bệnh nhân không hợp lệ. Vui lòng kiểm tra lại mã bệnh nhân.",
          details: error.response?.data,
        };
      }

      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.title ||
          "Có lỗi xảy ra khi hoàn thành yêu cầu máu",
        details: error.response?.data,
      };
    }
  },

  // Update blood request status
  updateBloodRequestStatus: async (requestId, newStatus, notes = null) => {
    try {
      const response = await axiosInstance.patch(
        `${BLOOD_REQUEST_API}/${requestId}/status`,
        {
          status: newStatus,
          notes: notes,
          updatedAt: new Date().toISOString(),
        }
      );

      return {
        success: true,
        data: response.data,
        message: "Cập nhật trạng thái yêu cầu máu thành công",
      };
    } catch (error) {
      return {
        success: false,
        error:
          error.response?.data?.message ||
          "Có lỗi xảy ra khi cập nhật trạng thái yêu cầu máu",
        details: error.response?.data,
      };
    }
  },
};

export default bloodRequestService;
