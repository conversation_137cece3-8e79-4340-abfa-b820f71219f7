import React from "react";
import { Row, Col, Card, Statistic, Spin, Typography, List, Badge } from "antd";
import {
  UserOutlined,
  FileTextOutlined,
  TeamOutlined,
  BellOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  CloseCircleOutlined,
} from "@ant-design/icons";
import { Pie, Column, Line, Area } from "@ant-design/charts";
import AdminLayout from "../../components/admin/AdminLayout";
import WelcomeBanner from "../../components/admin/dashboard/WelcomeBanner";
import AdminPageHeader from "../../components/admin/AdminPageHeader";
import { getUserName } from "../../utils/userUtils";
import useRequest from "../../hooks/useFetchData";
import userInfoService from "../../services/userInfoService";
import bloodRequestService from "../../services/bloodRequestService";
import { getBloodArticles } from "../../services/bloodArticleService";
import { fetchAllNews } from "../../services/newsService";
import "../../styles/pages/AdminDashboard.scss";

const { Title, Text } = Typography;

const containerStyle = {
  width: "100%",
  maxWidth: 1200,
  margin: "0 auto",
};

const cardStyle = {
  minHeight: 120,
  display: "flex",
  flexDirection: "column",
  justifyContent: "center",
  borderRadius: 12,
  boxShadow: "0 2px 8px #f0f1f2",
};

const sectionCardStyle = {
  minHeight: 420,
  display: "flex",
  flexDirection: "column",
  justifyContent: "center",
  borderRadius: 12,
  boxShadow: "0 2px 8px #f0f1f2",
};

const fetchDashboardData = async () => {
  try {
    // Fetch data from multiple APIs in parallel
    const [users, bloodRequests, articles, news] = await Promise.all([
      userInfoService.getAllUsers().catch((err) => {
        console.error("Error fetching users:", err);
        return [];
      }),
      bloodRequestService
        .getBloodRequests()
        .then((result) => {
          return result.success ? result.data : [];
        })
        .catch((err) => {
          console.error("Error fetching blood requests:", err);
          return [];
        }),
      getBloodArticles().catch((err) => {
        console.error("Error fetching articles:", err);
        return [];
      }),
      fetchAllNews().catch((err) => {
        console.error("Error fetching news:", err);
        return [];
      }),
    ]);

    // Process users by role
    const usersByRole = {
      admin: 0,
      manager: 0,
      doctor: 0,
      member: 0,
    };

    const activeUsers = users.filter(
      (user) => user.status === 1 || user.status === "active"
    );

    users.forEach((user) => {
      switch (user.roleID) {
        case 4:
          usersByRole.admin++;
          break;
        case 3:
          usersByRole.manager++;
          break;
        case 2:
          usersByRole.doctor++;
          break;
        case 1:
        default:
          usersByRole.member++;
          break;
      }
    });

    // Process blood requests by status
    const pendingRequests = bloodRequests.filter(
      (req) => req.status === 0
    ).length;
    const completedRequests = bloodRequests.filter(
      (req) => req.status === 2
    ).length;

    // Calculate total posts (articles + news)
    const totalPosts = (articles?.length || 0) + (news?.length || 0);

    // Generate chart data
    const bloodRequestStatusData = [
      { status: "Chờ xử lý", count: pendingRequests, color: "#faad14" },
      {
        status: "Đã chấp nhận",
        count: bloodRequests.filter((req) => req.status === 1).length,
        color: "#1890ff",
      },
      { status: "Hoàn thành", count: completedRequests, color: "#52c41a" },
      {
        status: "Từ chối",
        count: bloodRequests.filter((req) => req.status === 3).length,
        color: "#ff4d4f",
      },
    ].filter((item) => item.count > 0);

    // Generate system notifications based on real data
    const notifications = [];

    // System health notification
    notifications.push({
      id: 1,
      type: "success",
      title: "Hệ thống hoạt động bình thường",
      message: `Đang quản lý ${users.length} người dùng và ${totalPosts} bài viết.`,
      time: "Vừa cập nhật",
    });

    // Pending requests notification
    if (pendingRequests > 0) {
      notifications.push({
        id: 2,
        type: "warning",
        title: `${pendingRequests} yêu cầu máu đang chờ xử lý`,
        message: "Cần xem xét và phê duyệt các yêu cầu máu mới.",
        time: "Cần xử lý",
      });
    }

    // Recent activity notification
    if (completedRequests > 0) {
      notifications.push({
        id: 3,
        type: "info",
        title: `${completedRequests} yêu cầu máu đã hoàn thành`,
        message: "Hệ thống đang hoạt động hiệu quả.",
        time: "Hôm nay",
      });
    }

    return {
      totalUsers: users.length,
      totalPosts: totalPosts,
      totalRequests: bloodRequests.length,
      totalActivities: bloodRequests.length + totalPosts + users.length, // Total system activities
      totalAdmins: usersByRole.admin,
      totalManagers: usersByRole.manager,
      totalDoctors: usersByRole.doctor,
      totalMembers: usersByRole.member,
      activeUsers: activeUsers.length,
      pendingRequests,
      completedRequests,
      notifications,
      bloodRequestStatusData,
    };
  } catch (error) {
    console.error("Error fetching dashboard data:", error);

    // Return fallback data if APIs fail
    return {
      totalUsers: 0,
      totalPosts: 0,
      totalRequests: 0,
      totalActivities: 0,
      totalAdmins: 0,
      totalManagers: 0,
      totalDoctors: 0,
      totalMembers: 0,
      activeUsers: 0,
      pendingRequests: 0,
      completedRequests: 0,
      notifications: [
        {
          id: 1,
          type: "error",
          title: "Lỗi kết nối API",
          message: "Không thể tải dữ liệu dashboard. Vui lòng thử lại.",
          time: "Vừa xảy ra",
        },
      ],
      bloodRequestStatusData: [],
    };
  }
};

const AdminDashboard = () => {
  const { data: dashboardData, loading } = useRequest(fetchDashboardData, []);

  // Ensure dashboardData is not null and has default values
  const safeData = dashboardData || {
    totalAdmins: 0,
    totalManagers: 0,
    totalDoctors: 0,
    totalMembers: 0,
    totalUsers: 0,
    totalPosts: 0,
    totalRequests: 0,
    totalActivities: 0,
    activeUsers: 0,
    pendingRequests: 0,
    completedRequests: 0,
    notifications: [],
    bloodRequestStatusData: [],
  };

  // Pie chart config for user roles with better colors and labels
  const userTypePieData = [
    {
      type: "Quản trị viên",
      value: safeData.totalAdmins,
      color: "#722ed1", // Purple
    },
    {
      type: "Quản lý",
      value: safeData.totalManagers,
      color: "#fa8c16", // Orange
    },
    {
      type: "Bác sĩ",
      value: safeData.totalDoctors,
      color: "#1890ff", // Blue
    },
    {
      type: "Thành viên",
      value: safeData.totalMembers,
      color: "#52c41a", // Green
    },
  ].filter((item) => item.value > 0); // Only show roles that have users

  const pieConfig = {
    data: userTypePieData,
    angleField: "value",
    colorField: "type",
    radius: 0.8,
    innerRadius: 0.4, // Donut chart
    label: {
      type: "outer",
      content: "{name}\n{value} người ({percentage})",
      style: {
        fontSize: 12,
        fontWeight: "500",
      },
    },
    legend: {
      position: "bottom",
      itemName: {
        style: {
          fontSize: 14,
          fontWeight: "500",
        },
      },
    },
    color: userTypePieData.map((item) => item.color),
    height: 300,
    statistic: {
      title: {
        style: {
          fontSize: "16px",
          fontWeight: "600",
        },
        content: "Tổng cộng",
      },
      content: {
        style: {
          fontSize: "24px",
          fontWeight: "bold",
          color: "#1890ff",
        },
        content: safeData.totalUsers.toString(),
      },
    },
    interactions: [
      {
        type: "element-active",
      },
      {
        type: "pie-statistic-active",
      },
    ],
  };

  if (loading) {
    return (
      <div className="dashboard-loading">
        <Spin size="large" />
      </div>
    );
  }

  const adminName = getUserName();

  return (
    <AdminLayout>
      <WelcomeBanner adminName={adminName} />
      <div style={containerStyle}>
        <AdminPageHeader
          title="Tổng quan hệ thống"
          icon={<PieChartOutlined />}
          subtitle="Xem nhanh các số liệu tổng quan của hệ thống quản trị"
        />
        <div className="admin-dashboard">
          <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
            <Col xs={24} sm={12} md={6}>
              <Card
                bordered={false}
                style={cardStyle}
                className="statistic-card"
              >
                <Statistic
                  title="Tổng người dùng"
                  value={safeData.totalUsers}
                  prefix={<UserOutlined style={{ color: "#1890ff" }} />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card
                bordered={false}
                style={cardStyle}
                className="statistic-card"
              >
                <Statistic
                  title="Tổng bài viết"
                  value={safeData.totalPosts}
                  prefix={<FileTextOutlined style={{ color: "#722ed1" }} />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card
                bordered={false}
                style={cardStyle}
                className="statistic-card"
              >
                <Statistic
                  title="Tổng yêu cầu"
                  value={safeData.totalRequests}
                  prefix={<TeamOutlined style={{ color: "#faad14" }} />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card
                bordered={false}
                style={cardStyle}
                className="statistic-card"
              >
                <Statistic
                  title="Tổng hoạt động"
                  value={safeData.totalActivities}
                  prefix={<PieChartOutlined style={{ color: "#52c41a" }} />}
                />
              </Card>
            </Col>
          </Row>

          {/* Additional Statistics Row */}
          <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
            <Col xs={24} sm={12} md={6}>
              <Card
                bordered={false}
                style={cardStyle}
                className="statistic-card"
              >
                <Statistic
                  title="Người dùng hoạt động"
                  value={safeData.activeUsers}
                  prefix={<CheckCircleOutlined style={{ color: "#52c41a" }} />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card
                bordered={false}
                style={cardStyle}
                className="statistic-card"
              >
                <Statistic
                  title="Yêu cầu chờ xử lý"
                  value={safeData.pendingRequests}
                  prefix={<WarningOutlined style={{ color: "#faad14" }} />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card
                bordered={false}
                style={cardStyle}
                className="statistic-card"
              >
                <Statistic
                  title="Yêu cầu hoàn thành"
                  value={safeData.completedRequests}
                  prefix={<CheckCircleOutlined style={{ color: "#1890ff" }} />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card
                bordered={false}
                style={cardStyle}
                className="statistic-card"
              >
                <Statistic
                  title="Tỷ lệ hoàn thành"
                  value={
                    safeData.totalRequests > 0
                      ? Math.round(
                          (safeData.completedRequests /
                            safeData.totalRequests) *
                            100
                        )
                      : 0
                  }
                  suffix="%"
                  prefix={<PieChartOutlined style={{ color: "#722ed1" }} />}
                />
              </Card>
            </Col>
          </Row>

          <Row gutter={[24, 24]}>
            <Col xs={24} md={12}>
              <Card bordered={false} style={sectionCardStyle}>
                <Title level={4} style={{ marginBottom: 16 }}>
                  Phân loại người dùng
                </Title>
                <Pie {...pieConfig} />
              </Card>
            </Col>

          </Row>


        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;
