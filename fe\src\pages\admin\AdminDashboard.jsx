import React from "react";
import { Row, Col, Card, Statistic, Spin, Typography } from "antd";
import {
  UserOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  PieChartOutlined,
  CloseCircleOutlined,
} from "@ant-design/icons";
import { Pie } from "@ant-design/charts";
import AdminLayout from "../../components/admin/AdminLayout";
import WelcomeBanner from "../../components/admin/dashboard/WelcomeBanner";
import AdminPageHeader from "../../components/admin/AdminPageHeader";
import { getUserName } from "../../utils/userUtils";
import useRequest from "../../hooks/useFetchData";
import userInfoService from "../../services/userInfoService";
import { getBloodArticles } from "../../services/bloodArticleService";
import { fetchAllNews } from "../../services/newsService";
import "../../styles/pages/AdminDashboard.scss";

const { Title } = Typography;

const containerStyle = {
  width: "100%",
  maxWidth: 1200,
  margin: "0 auto",
};

const cardStyle = {
  minHeight: 120,
  display: "flex",
  flexDirection: "column",
  justifyContent: "center",
  borderRadius: 12,
  boxShadow: "0 2px 8px #f0f1f2",
};

const sectionCardStyle = {
  minHeight: 420,
  display: "flex",
  flexDirection: "column",
  justifyContent: "center",
  borderRadius: 12,
  boxShadow: "0 2px 8px #f0f1f2",
};

const fetchDashboardData = async () => {
  try {
    // Fetch data from APIs - only what we need for admin dashboard
    const [users, articles, news] = await Promise.all([
      userInfoService.getAllUsers().catch((err) => {
        console.error("Error fetching users:", err);
        return [];
      }),
      getBloodArticles().catch((err) => {
        console.error("Error fetching articles:", err);
        return [];
      }),
      fetchAllNews().catch((err) => {
        console.error("Error fetching news:", err);
        return [];
      }),
    ]);

    // Process users by role
    const usersByRole = {
      admin: 0,
      manager: 0,
      doctor: 0,
      member: 0,
    };

    // Process users by status and role
    let activeUsers = 0;
    let suspendedUsers = 0;

    users.forEach((user) => {
      // Count by role
      switch (user.roleID) {
        case 4:
          usersByRole.admin++;
          break;
        case 3:
          usersByRole.manager++;
          break;
        case 2:
          usersByRole.doctor++;
          break;
        case 1:
        default:
          usersByRole.member++;
          break;
      }

      // Count by status (1 = active, 0 = suspended)
      if (user.status === 1) {
        activeUsers++;
      } else if (user.status === 0) {
        suspendedUsers++;
      }
    });

    // Calculate total posts (articles + news)
    const totalPosts = (articles?.length || 0) + (news?.length || 0);

    return {
      totalUsers: users.length,
      totalPosts: totalPosts,
      totalAdmins: usersByRole.admin,
      totalManagers: usersByRole.manager,
      totalDoctors: usersByRole.doctor,
      totalMembers: usersByRole.member,
      activeUsers,
      suspendedUsers,
    };
  } catch (error) {
    console.error("Error fetching dashboard data:", error);

    // Return fallback data if APIs fail
    return {
      totalUsers: 0,
      totalPosts: 0,
      totalAdmins: 0,
      totalManagers: 0,
      totalDoctors: 0,
      totalMembers: 0,
      activeUsers: 0,
      suspendedUsers: 0,
    };
  }
};

const AdminDashboard = () => {
  const { data: dashboardData, loading } = useRequest(fetchDashboardData, []);

  // Ensure dashboardData is not null and has default values
  const safeData = dashboardData || {
    totalAdmins: 0,
    totalManagers: 0,
    totalDoctors: 0,
    totalMembers: 0,
    totalUsers: 0,
    totalPosts: 0,
    activeUsers: 0,
    suspendedUsers: 0,
  };

  // Pie chart config for user roles - using same style as manager/doctor dashboards
  const userTypePieData = [
    {
      type: "Quản trị viên",
      value: safeData.totalAdmins || 0,
    },
    {
      type: "Quản lý",
      value: safeData.totalManagers || 0,
    },
    {
      type: "Bác sĩ",
      value: safeData.totalDoctors || 0,
    },
    {
      type: "Thành viên",
      value: safeData.totalMembers || 0,
    },
  ].filter((item) => item.value > 0); // Only show roles that have users

  // Debug: Log pie chart data
  console.log("Admin Dashboard - Pie chart data:", userTypePieData);
  console.log("Admin Dashboard - Safe data:", safeData);

  const pieConfig = {
    data: userTypePieData,
    angleField: "value",
    colorField: "type",
    radius: 0.8,
    label: {
      type: "outer",
      content: "{name}: {percentage}",
      style: {
        fontSize: 14,
        textAlign: "center",
      },
    },
    legend: {
      position: "bottom",
    },
    interactions: [{ type: "element-active" }],
  };

  if (loading) {
    return (
      <div className="dashboard-loading">
        <Spin size="large" />
      </div>
    );
  }

  const adminName = getUserName();

  return (
    <AdminLayout>
      <WelcomeBanner adminName={adminName} />
      <div style={containerStyle}>
        <AdminPageHeader
          title="Tổng quan hệ thống"
          icon={<PieChartOutlined />}
          subtitle="Xem nhanh các số liệu tổng quan của hệ thống quản trị"
        />
        <div className="admin-dashboard">
          <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
            <Col xs={24} sm={12} md={6}>
              <Card
                style={cardStyle}
                className="statistic-card"
              >
                <Statistic
                  title="Tổng người dùng hoạt động"
                  value={safeData.activeUsers}
                  prefix={<CheckCircleOutlined style={{ color: "#52c41a" }} />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card
                style={cardStyle}
                className="statistic-card"
              >
                <Statistic
                  title="Tổng người dùng bị đình chỉ"
                  value={safeData.suspendedUsers}
                  prefix={<CloseCircleOutlined style={{ color: "#ff4d4f" }} />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card
                style={cardStyle}
                className="statistic-card"
              >
                <Statistic
                  title="Tổng bài viết"
                  value={safeData.totalPosts}
                  prefix={<FileTextOutlined style={{ color: "#722ed1" }} />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card
                style={cardStyle}
                className="statistic-card"
              >
                <Statistic
                  title="Tổng người dùng"
                  value={safeData.totalUsers}
                  prefix={<UserOutlined style={{ color: "#1890ff" }} />}
                />
              </Card>
            </Col>
          </Row>



          <Row gutter={[24, 24]}>
            <Col xs={24} md={12}>
              <Card style={sectionCardStyle}>
                <Title level={4} style={{ marginBottom: 16 }}>
                  Phân loại người dùng
                </Title>
                {userTypePieData.length > 0 ? (
                  <Pie {...pieConfig} />
                ) : (
                  <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
                    Không có dữ liệu để hiển thị
                  </div>
                )}
              </Card>
            </Col>

          </Row>


        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;
