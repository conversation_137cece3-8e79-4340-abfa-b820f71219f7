import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { ToastContainer, Toast } from 'react-bootstrap';
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  CloseCircleOutlined,
  LoadingOutlined
} from '@ant-design/icons';
import { setGlobalToastInstance } from '../utils/toastUtils';
import '../styles/toast.scss';

const ToastContext = createContext();

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

export const ToastProvider = ({ children }) => {
  const [toasts, setToasts] = useState([]);

  const addToast = useCallback((toast) => {
    const id = Date.now() + Math.random();
    const newToast = {
      id,
      ...toast,
      show: true,
    };
    
    setToasts(prev => [...prev, newToast]);
    
    // Auto remove toast after duration
    setTimeout(() => {
      removeToast(id);
    }, toast.duration || 4000);
    
    return id;
  }, []);

  const removeToast = useCallback((id) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  const success = useCallback((message, options = {}) => {
    return addToast({
      variant: 'success',
      message,
      duration: 4000,
      ...options,
    });
  }, [addToast]);

  const error = useCallback((message, options = {}) => {
    return addToast({
      variant: 'danger',
      message,
      duration: 5000,
      ...options,
    });
  }, [addToast]);

  const warning = useCallback((message, options = {}) => {
    return addToast({
      variant: 'warning',
      message,
      duration: 4000,
      ...options,
    });
  }, [addToast]);

  const info = useCallback((message, options = {}) => {
    return addToast({
      variant: 'info',
      message,
      duration: 4000,
      ...options,
    });
  }, [addToast]);

  const loading = useCallback((message, options = {}) => {
    return addToast({
      variant: 'info',
      message,
      duration: 0, // Don't auto-hide loading toasts
      isLoading: true,
      ...options,
    });
  }, [addToast]);

  const getIcon = (variant, isLoading = false) => {
    const iconStyle = { fontSize: '18px', marginRight: '8px' };
    
    if (isLoading) {
      return <LoadingOutlined style={{ ...iconStyle, color: '#17a2b8' }} spin />;
    }
    
    switch (variant) {
      case 'success':
        return <CheckCircleOutlined style={{ ...iconStyle, color: '#28a745' }} />;
      case 'danger':
        return <CloseCircleOutlined style={{ ...iconStyle, color: '#dc3545' }} />;
      case 'warning':
        return <ExclamationCircleOutlined style={{ ...iconStyle, color: '#ffc107' }} />;
      case 'info':
        return <InfoCircleOutlined style={{ ...iconStyle, color: '#17a2b8' }} />;
      default:
        return <CheckCircleOutlined style={{ ...iconStyle, color: '#28a745' }} />;
    }
  };

  const getHeaderText = (variant, isLoading = false) => {
    if (isLoading) return 'Đang xử lý';
    
    switch (variant) {
      case 'success':
        return 'Thành công';
      case 'danger':
        return 'Lỗi';
      case 'warning':
        return 'Cảnh báo';
      case 'info':
        return 'Thông tin';
      default:
        return 'Thông báo';
    }
  };

  const getBgClass = (variant) => {
    switch (variant) {
      case 'success':
        return 'bg-success';
      case 'danger':
        return 'bg-danger';
      case 'warning':
        return 'bg-warning';
      case 'info':
        return 'bg-info';
      default:
        return 'bg-success';
    }
  };

  const value = {
    success,
    error,
    warning,
    info,
    loading,
    removeToast,
  };

  // Set global toast instance for utility functions
  useEffect(() => {
    setGlobalToastInstance(value);
  }, [value]);

  return (
    <ToastContext.Provider value={value}>
      {children}
      <ToastContainer
        position="top-end"
        className="toast-container"
      >
        {toasts.map((toast) => (
          <Toast
            key={toast.id}
            show={toast.show}
            onClose={() => removeToast(toast.id)}
            delay={toast.duration}
            autohide={toast.duration > 0}
            className={`toast-${toast.variant}`}
          >
            <Toast.Header>
              <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                {getIcon(toast.variant, toast.isLoading)}
                <strong style={{ marginLeft: '8px', marginRight: 'auto' }}>
                  {getHeaderText(toast.variant, toast.isLoading)}
                </strong>
                <small style={{ opacity: 0.8 }}>vừa xong</small>
              </div>
            </Toast.Header>
            <Toast.Body>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                {toast.isLoading && (
                  <div className="loading-spinner" style={{ marginRight: '10px' }}></div>
                )}
                <span>{toast.message}</span>
              </div>
            </Toast.Body>
          </Toast>
        ))}
      </ToastContainer>
    </ToastContext.Provider>
  );
};

export default ToastContext;
