.blood-inventory-alert-chart {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

  .loading-container {
    text-align: center;
    padding: 40px 20px;
    
    p {
      margin-top: 16px;
      color: #666;
    }
  }

  .retry-button {
    background: #1890ff;
    color: white;
    border: none;
    padding: 4px 12px;
    border-radius: 4px;
    cursor: pointer;
    
    &:hover {
      background: #40a9ff;
    }
  }

  .chart-header {
    margin-bottom: 20px;

    h3 {
      margin: 0 0 8px 0;
      color: #262626;
      font-size: 20px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .chart-subtitle {
      margin: 0 0 16px 0;
      color: #666;
      font-size: 14px;
    }

    .chart-filters {
      display: flex;
      align-items: center;
      gap: 12px;

      .filter-label {
        font-size: 14px;
        color: #666;
        font-weight: 500;
      }

      .ant-radio-group {
        .ant-radio-button-wrapper {
          border-radius: 6px;
          font-size: 13px;

          &:first-child {
            border-radius: 6px 0 0 6px;
          }

          &:last-child {
            border-radius: 0 6px 6px 0;
          }

          &.ant-radio-button-wrapper-checked {
            background: #D91022;
            border-color: #D91022;
            color: white;

            &:hover {
              background: #ff4d4f;
              border-color: #ff4d4f;
            }
          }
        }
      }
    }
  }

  .chart-content {
    margin-bottom: 20px;
  }

  .alert-tooltip {
    background: white;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 8px 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    
    .tooltip-label {
      margin: 0 0 4px 0;
      font-weight: 600;
      color: #262626;
    }
    
    .tooltip-value {
      margin: 0 0 2px 0;
      font-size: 12px;
    }
  }

  .alert-summary {
    padding: 16px;
    background: #fafafa;
    border-radius: 8px;
    border-left: 4px solid #D91022;

    .summary-item {
      text-align: center;
      padding: 12px;
      background: white;
      border-radius: 6px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      
      .summary-label {
        display: block;
        font-size: 12px;
        color: #666;
        margin-bottom: 4px;
      }
      
      .summary-value {
        display: block;
        font-size: 20px;
        font-weight: 700;
      }

      &.critical {
        .summary-value {
          color: #D91022;
        }
      }

      &.low {
        .summary-value {
          color: #fa8c16;
        }
      }

      &.total {
        .summary-value {
          color: #1890ff;
        }
      }

      &.rare {
        .summary-value {
          color: #722ed1;
        }
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    .chart-header {
      h3 {
        font-size: 18px;
      }

      .chart-subtitle {
        font-size: 13px;
      }
    }

    .alert-tabs {
      .ant-tabs-nav {
        .ant-tabs-tab {
          padding: 6px 12px;
          font-size: 13px;
        }
      }
    }

    .alert-summary {
      padding: 12px;
      
      .summary-item {
        padding: 8px;
        
        .summary-label {
          font-size: 11px;
        }
        
        .summary-value {
          font-size: 16px;
        }
      }
    }
  }

  @media (max-width: 576px) {
    .chart-header {
      h3 {
        font-size: 16px;
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
      }
    }

    .alert-tabs {
      .ant-tabs-nav {
        .ant-tabs-nav-wrap {
          .ant-tabs-nav-list {
            .ant-tabs-tab {
              padding: 4px 8px;
              font-size: 12px;
            }
          }
        }
      }
    }

    .alert-summary {
      .summary-item {
        .summary-label {
          font-size: 10px;
        }
        
        .summary-value {
          font-size: 14px;
        }
      }
    }
  }
}
