/**
 * Blood Request Status Utilities
 * Maps between numeric status codes and display text
 */

// Status mapping từ API
export const BLOOD_REQUEST_STATUS = {
  0: "Đang chờ xử lý",
  1: "<PERSON><PERSON> duyệt",
  2: "<PERSON><PERSON><PERSON> thành",
  3: "Từ chối",
  4: "<PERSON><PERSON> hủy",
};

// Reverse mapping để tìm status code từ text
export const BLOOD_REQUEST_STATUS_REVERSE = Object.entries(
  BLOOD_REQUEST_STATUS
).reduce((acc, [code, text]) => {
  acc[text.toLowerCase()] = parseInt(code);
  return acc;
}, {});

/**
 * Check if request is pending (needs action)
 * @param {Object} request - Blood request object
 * @returns {boolean}
 */
export const isPendingRequest = (request) => {
  // Handle numeric status (API returns 0 for pending)
  if (typeof request.status === "number") {
    return request.status === 0; // 0 = "Đang chờ xử lý"
  }

  // Handle string status for backward compatibility
  const status = request.status?.toLowerCase();
  return (
    status === "pending" ||
    status === "chờ xử lý" ||
    status === "đang chờ xử lý" ||
    request.status === "PENDING"
  );
};

/**
 * Get display text for status code
 * @param {number|string} status - Status code or text
 * @returns {string}
 */
export const getStatusDisplayText = (status) => {
  if (typeof status === "number") {
    return BLOOD_REQUEST_STATUS[status] || `Không xác định (${status})`;
  }
  return status; // Return as-is if already string
};

/**
 * Get status code from display text
 * @param {string} displayText - Display text
 * @returns {number|null}
 */
export const getStatusCode = (displayText) => {
  const code = BLOOD_REQUEST_STATUS_REVERSE[displayText.toLowerCase()];
  return code !== undefined ? code : null;
};

/**
 * Check if status represents completed state
 * @param {number|string} status - Status
 * @returns {boolean}
 */
export const isCompletedStatus = (status) => {
  if (typeof status === "number") {
    return status === 2; // 2 = "Hoàn thành"
  }
  const statusLower = status?.toLowerCase();
  return statusLower === "completed" || statusLower === "hoàn thành";
};

/**
 * Check if status represents approved state
 * @param {number|string} status - Status
 * @returns {boolean}
 */
export const isApprovedStatus = (status) => {
  if (typeof status === "number") {
    return status === 1; // 1 = "Đã duyệt"
  }
  const statusLower = status?.toLowerCase();
  return statusLower === "approved" || statusLower === "đã duyệt";
};

/**
 * Check if status represents rejected state
 * @param {number|string} status - Status
 * @returns {boolean}
 */
export const isRejectedStatus = (status) => {
  if (typeof status === "number") {
    return status === 3; // 3 = "Từ chối"
  }
  const statusLower = status?.toLowerCase();
  return statusLower === "rejected" || statusLower === "từ chối";
};

/**
 * Get status color for UI display
 * @param {number|string} status - Status
 * @returns {string} CSS color
 */
export const getStatusColor = (status) => {
  if (isPendingRequest({ status })) return "#faad14"; // Orange
  if (isApprovedStatus(status)) return "#52c41a"; // Green
  if (isCompletedStatus(status)) return "#1677ff"; // Blue
  if (isRejectedStatus(status)) return "#ff4d4f"; // Red
  return "#d9d9d9"; // Gray for unknown
};

export default {
  BLOOD_REQUEST_STATUS,
  BLOOD_REQUEST_STATUS_REVERSE,
  isPendingRequest,
  getStatusDisplayText,
  getStatusCode,
  isCompletedStatus,
  isApprovedStatus,
  isRejectedStatus,
  getStatusColor,
};
