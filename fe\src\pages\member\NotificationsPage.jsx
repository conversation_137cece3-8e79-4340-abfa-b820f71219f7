import { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import MemberNavbar from "../../components/member/MemberNavbar";
import NotificationService from "../../services/notificationService";
import { useNotification } from "../../contexts/NotificationContext";
import "../../styles/pages/NotificationsPage.scss";

const NotificationsPage = () => {
  const {
    notifications,
    unreadCount,
    loading,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    loadNotifications,
  } = useNotification();
  const location = useLocation();
  const [filteredNotifications, setFilteredNotifications] = useState([]);
  const [filter, setFilter] = useState("all"); // all, unread, read
  const [typeFilter, setTypeFilter] = useState("all");

  // Refresh notifications when page is accessed (only once)
  useEffect(() => {
    if (location.pathname === '/member/notifications') {
      console.log("NotificationsPage: Page accessed, refreshing notifications");
      loadNotifications(true, false); // Force refresh, not silent
    }
  }, [location.pathname]); // Removed loadNotifications dependency

  useEffect(() => {
    console.log(
      "NotificationsPage: Notifications changed, re-applying filters",
      { notificationsCount: notifications.length }
    );
    applyFilters();
  }, [notifications, filter, typeFilter]); // Removed lastUpdated dependency for cleaner logic

  const applyFilters = () => {
    let filtered = [...notifications];

    // Filter by read status
    if (filter === "unread") {
      filtered = filtered.filter((n) => !(n.isRead || n.IsRead));
    } else if (filter === "read") {
      filtered = filtered.filter((n) => n.isRead || n.IsRead);
    }

    // Filter by type
    if (typeFilter !== "all") {
      filtered = filtered.filter((n) => (n.type || n.Type) === typeFilter);
    }

    setFilteredNotifications(filtered);
  };

  const handleMarkAsRead = async (notificationId) => {
    if (!notificationId) return;
    console.log("NotificationsPage: Marking as read:", notificationId);
    await markAsRead(notificationId);
    // No need to force update - context will handle this naturally
  };

  const handleMarkAllAsRead = async () => {
    console.log("NotificationsPage: Marking all as read");
    await markAllAsRead();
    // No need to force update - context will handle this naturally
  };

  const handleDeleteNotification = async (notificationId) => {
    if (!notificationId) return;
    console.log("🗑️ NotificationsPage: Deleting notification:", notificationId);
    await deleteNotification(notificationId);
    // No need to force update - context will handle this naturally
  };

  // Function to get display name for profile fields
  const getFieldDisplayName = (fieldName) => {
    const fieldNames = {
      // Personal Information
      fullName: "Họ và tên",
      firstName: "Tên",
      lastName: "Họ",
      email: "Email",
      phone: "Số điện thoại",
      dateOfBirth: "Ngày sinh",
      gender: "Giới tính",
      idNumber: "CCCD/CMND",

      // Address Information
      address: "Địa chỉ",
      city: "Thành phố",
      district: "Quận/Huyện",
      ward: "Phường/Xã",
      zipCode: "Mã bưu điện",

      // Medical Information
      bloodGroup: "Nhóm máu ABO",
      rhType: "Yếu tố Rh",
      weight: "Cân nặng",
      height: "Chiều cao",
      medicalHistory: "Tiền sử bệnh",
      allergies: "Dị ứng",
      medications: "Thuốc đang sử dụng",

      // Emergency Contact
      emergencyContactName: "Tên người liên hệ khẩn cấp",
      emergencyContactPhone: "SĐT người liên hệ khẩn cấp",
      emergencyContactRelation: "Mối quan hệ",

      // Other
      occupation: "Nghề nghiệp",
      company: "Công ty",
      notes: "Ghi chú",
    };

    return fieldNames[fieldName] || fieldName;
  };

  const getNotificationAction = (notification) => {
    switch (notification.type || notification.Type) {
      case "Alert":
        return (
          <Link to="/member#emergency-section" className="notification-action">
            Xem yêu cầu máu
          </Link>
        );
      case "Reminder":
        return (
          <Link to="/member/activity-history" className="notification-action">
            Xem lịch hẹn
          </Link>
        );
      case "Report":
        return (
          <Link to="/member/health-records" className="notification-action">
            Xem báo cáo
          </Link>
        );
      // Legacy support for old notification types
      case "urgent_request":
        return (
          <Link to="/member#emergency-section" className="notification-action">
            Xem yêu cầu máu
          </Link>
        );
      case "appointment_reminder":
      case "donation_reminder":
        return (
          <Link to="/member/activity-history" className="notification-action">
            Xem lịch hẹn
          </Link>
        );
      case "health_check":
        return (
          <Link to="/member/health-records" className="notification-action">
            Xem kết quả
          </Link>
        );
      default:
        return null;
    }
  };

  const notificationTypes = [
    { value: "all", label: "Tất cả" },
    { value: "Reminder", label: "Nhắc nhở" },
    { value: "Alert", label: "Cảnh báo" },
    { value: "Report", label: "Báo cáo" },
    // Legacy support
    { value: "donation_reminder", label: "Nhắc nhở hiến máu (cũ)" },
    { value: "urgent_request", label: "Yêu cầu khẩn cấp (cũ)" },
    { value: "appointment_reminder", label: "Nhắc nhở lịch hẹn (cũ)" },
  ];

  return (
    <div className="notifications-page">
      <MemberNavbar />

      <div className="notifications-content">
        <div className="page-header">
          <div>
            <h1>🔔 Thông báo</h1>
            <p>Quản lý tất cả thông báo của bạn</p>
            {unreadCount > 0 && (
              <div className="unread-summary">
                Bạn có {unreadCount} thông báo chưa đọc
              </div>
            )}
          </div>
          <div className="header-actions">
            <button
              className="btn btn-secondary"
              onClick={() => loadNotifications(true, false)}
              disabled={loading}
            >
              {loading ? "Đang tải..." : "Làm mới"}
            </button>
            {unreadCount > 0 && (
              <button className="btn btn-primary" onClick={handleMarkAllAsRead}>
                Đánh dấu tất cả đã đọc
              </button>
            )}
          </div>
        </div>

        {/* Statistics */}
        <div className="notification-stats">
          <div className="stat-card">
            <h3>Tổng thông báo</h3>
            <p className="stat-number">{notifications.length}</p>
          </div>
          <div className="stat-card">
            <h3>Chưa đọc</h3>
            <p className="stat-number unread">{unreadCount}</p>
          </div>
          <div className="stat-card">
            <h3>Đã đọc</h3>
            <p className="stat-number read">
              {notifications.length - unreadCount}
            </p>
          </div>
          <div className="stat-card">
            <h3>Khẩn cấp</h3>
            <p className="stat-number urgent">
              {
                notifications.filter(
                  (n) =>
                    n.type === "Alert" ||
                    n.Type === "Alert" ||
                    n.type === "urgent_request"
                ).length
              }
            </p>
          </div>
        </div>

        {/* Filters */}
        <div className="filters-section">
          <div className="filter-group">
            <label>Trạng thái:</label>
            <select value={filter} onChange={(e) => setFilter(e.target.value)}>
              <option value="all">Tất cả</option>
              <option value="unread">Chưa đọc ({unreadCount})</option>
              <option value="read">Đã đọc</option>
            </select>
          </div>

          <div className="filter-group">
            <label>Loại thông báo:</label>
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
            >
              {notificationTypes.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Notifications List */}
        <div className="notifications-section">
          {loading ? (
            <div className="loading-state">
              <div className="loading-spinner"></div>
              <p>Đang tải thông báo...</p>
            </div>
          ) : filteredNotifications.length === 0 ? (
            <div className="empty-state">
              <span className="empty-icon">📭</span>
              <h3>Không có thông báo nào</h3>
              <p>
                {filter === "unread"
                  ? "Bạn đã đọc hết tất cả thông báo!"
                  : "Chưa có thông báo nào được gửi đến bạn."}
              </p>
            </div>
          ) : (
            <div className="notifications-list">
              {filteredNotifications.map((notification, index) => {
                // Handle different possible ID field names from API
                const notificationId =
                  notification.id ||
                  notification.notificationId ||
                  notification.NotificationId ||
                  index;

                return (
                  <div
                    key={notificationId}
                    className={`notification-card ${
                      !(notification.isRead || notification.IsRead)
                        ? "unread"
                        : ""
                    }`}
                  >
                    <div className="notification-header">
                      <div className="notification-icon-title">
                        <span
                          className="notification-icon"
                          style={{
                            color: NotificationService.getNotificationColor(
                              notification.type || notification.Type
                            ),
                          }}
                        >
                          {NotificationService.getNotificationIcon(
                            notification.type || notification.Type
                          )}
                        </span>
                        <div className="notification-title">
                          {notification.title || notification.Title}
                        </div>
                        {!(notification.isRead || notification.IsRead) && (
                          <div className="unread-indicator">●</div>
                        )}
                      </div>
                      <div className="notification-time">
                        {NotificationService.formatNotificationTime(
                          notification.SentAt ||
                            notification.sentAt ||
                            notification.createdAt
                        )}
                      </div>
                    </div>

                    <div className="notification-body">
                      <div className="notification-message">
                        {notification.message || notification.Message}
                      </div>

                      {notification.data && (
                        <div className="notification-details">
                          {/* Donation Reminder Details */}
                          {notification.type === "donation_reminder" &&
                            notification.data.daysUntilEligible !==
                              undefined && (
                              <div className="detail-item">
                                <strong>Thời gian còn lại:</strong>{" "}
                                {notification.data.daysUntilEligible === 0
                                  ? "Có thể hiến ngay"
                                  : `${notification.data.daysUntilEligible} ngày`}
                              </div>
                            )}

                          {/* Profile Update Details */}
                          {notification.type === "account_update" &&
                            notification.data.changes && (
                              <div className="profile-changes">
                                <div className="detail-item">
                                  <strong>Thông tin đã thay đổi:</strong>
                                </div>
                                {notification.data.changes.map(
                                  (change, index) => (
                                    <div key={index} className="change-item">
                                      <div className="change-field">
                                        <strong>
                                          {getFieldDisplayName(change.field)}:
                                        </strong>
                                      </div>
                                      <div className="change-values">
                                        <span className="old-value">
                                          {change.oldValue ||
                                            "Chưa có thông tin"}
                                        </span>
                                        <span className="arrow">→</span>
                                        <span className="new-value">
                                          {change.newValue ||
                                            "Chưa có thông tin"}
                                        </span>
                                      </div>
                                    </div>
                                  )
                                )}
                              </div>
                            )}

                          {/* Blood Type */}
                          {notification.data.bloodType && (
                            <div className="detail-item">
                              <strong>Nhóm máu:</strong>
                              <span className="blood-type-badge">
                                {notification.data.bloodType}
                              </span>
                            </div>
                          )}

                          {/* Quantity */}
                          {notification.data.quantity && (
                            <div className="detail-item">
                              <strong>Số lượng:</strong>{" "}
                              {notification.data.quantity}
                            </div>
                          )}

                          {/* Appointment Date */}
                          {notification.data.appointmentDate && (
                            <div className="detail-item">
                              <strong>Ngày hẹn:</strong>{" "}
                              {new Date(
                                notification.data.appointmentDate
                              ).toLocaleDateString("vi-VN")}
                            </div>
                          )}

                          {/* Location */}
                          {notification.data.location && (
                            <div className="detail-item">
                              <strong>Địa điểm:</strong>{" "}
                              {notification.data.location}
                            </div>
                          )}
                        </div>
                      )}

                      <div className="notification-actions">
                        {getNotificationAction(notification)}

                        <div className="action-buttons">
                          {!notification.isRead && (
                            <button
                              className="btn btn-sm btn-outline"
                              onClick={() => handleMarkAsRead(notificationId)}
                            >
                              Đánh dấu đã đọc
                            </button>
                          )}
                          <button
                            className="btn btn-sm btn-danger"
                            onClick={() =>
                              handleDeleteNotification(notificationId)
                            }
                          >
                            Xóa
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default NotificationsPage;
