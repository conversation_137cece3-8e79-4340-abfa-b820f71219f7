/**
 * Event system for blood request status updates
 * Allows components to listen for and emit blood request status changes
 */

class BloodRequestEventEmitter {
  constructor() {
    this.listeners = new Map();
  }

  // Subscribe to blood request status updates
  subscribe(eventType, callback) {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set());
    }
    this.listeners.get(eventType).add(callback);

    // Return unsubscribe function
    return () => {
      const callbacks = this.listeners.get(eventType);
      if (callbacks) {
        callbacks.delete(callback);
        if (callbacks.size === 0) {
          this.listeners.delete(eventType);
        }
      }
    };
  }

  // Emit blood request status update event
  emit(eventType, data) {
    const callbacks = this.listeners.get(eventType);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in blood request event callback:', error);
        }
      });
    }
  }

  // Get number of listeners for debugging
  getListenerCount(eventType) {
    return this.listeners.get(eventType)?.size || 0;
  }
}

// Create singleton instance
const bloodRequestEvents = new BloodRequestEventEmitter();

// Event types
export const BLOOD_REQUEST_EVENTS = {
  STATUS_UPDATED: 'status_updated',
  REQUEST_CREATED: 'request_created',
  REQUEST_DELETED: 'request_deleted',
};

// Helper function to emit status update
export const emitBloodRequestStatusUpdate = (requestId, oldStatus, newStatus, requestData = null) => {
  bloodRequestEvents.emit(BLOOD_REQUEST_EVENTS.STATUS_UPDATED, {
    requestId,
    oldStatus,
    newStatus,
    requestData,
    timestamp: new Date().toISOString(),
  });
};

// Helper function to emit request creation
export const emitBloodRequestCreated = (requestData) => {
  bloodRequestEvents.emit(BLOOD_REQUEST_EVENTS.REQUEST_CREATED, {
    requestData,
    timestamp: new Date().toISOString(),
  });
};

// Helper function to emit request deletion
export const emitBloodRequestDeleted = (requestId) => {
  bloodRequestEvents.emit(BLOOD_REQUEST_EVENTS.REQUEST_DELETED, {
    requestId,
    timestamp: new Date().toISOString(),
  });
};

export default bloodRequestEvents;
