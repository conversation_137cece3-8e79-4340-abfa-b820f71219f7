import React from "react";
import { ReloadOutlined, DatabaseOutlined } from "@ant-design/icons";
import ManagerLayout from "../../components/manager/ManagerLayout";
import PageHeader from "../../components/manager/PageHeader";
import BloodInventoryViewPage from "../../components/shared/BloodInventoryViewPage";
import "../../styles/pages/manager/BloodInventoryManagement.scss";
import "../../styles/components/PageHeader.scss";

/**
 * Manager Blood Inventory Page
 * Wrapper component cho Manager sử dụng shared BloodInventoryViewPage
 * C<PERSON> đầy đủ tính năng: tabs lịch sử, nút nhập/xuất kho
 */
const ManagerBloodInventoryPage = () => {
  // Page Header Component cho Manager
  const ManagerPageHeader = ({ loadInventory, loading }) => (
    <PageHeader 
      title="Quản lý kho máu"
      description="Quản lý tồn kho máu, nhập/xuất kho và theo dõi lịch sử hoạt động"
      icon={DatabaseOutlined}
      actions={[
        {
          label: "Làm mới",
          icon: <ReloadOutlined />,
          onClick: loadInventory,
          loading: loading,
        },
      ]}
    />
  );

  return (
    <BloodInventoryViewPage
      showManagerFeatures={true} // Manager có đầy đủ tính năng
      pageHeaderComponent={ManagerPageHeader}
      layoutComponent={ManagerLayout}
    />
  );
};

export default ManagerBloodInventoryPage;
