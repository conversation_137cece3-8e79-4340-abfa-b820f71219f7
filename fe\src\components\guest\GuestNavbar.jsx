import React from "react";
import NavbarBase from "../common/NavbarBase";

/**
 * Navbar dành cho khách (chưa đăng nhập)
 * Sử dụng NavbarBase component với cấu hình cho guest
 */
const GuestNavbar = () => {
  // Danh sách các mục điều hướng chính
  const navItems = [
    { path: "/", label: "Trang chủ" },
    { path: "/blood-info", label: "Tài liệu" },
    { path: "/blog", label: "Tin tức" },
    { path: "/donation-guide", label: "Hướng dẫn hiến máu" },
  ];

  // <PERSON><PERSON><PERSON> nút hành động (đăng nhập, đăng ký)
  const actionItems = [
    { path: "/login", label: "Đăng nhập", className: "btn-login" },
    { path: "/register", label: "Đă<PERSON> ký", className: "btn-register" },
  ];

  return (
    <NavbarBase
      logoSrc="src/assets/images/logo.png"
      logoAlt="Blood Donation Logo"
      navItems={navItems}
      actionItems={actionItems}
      userInfo={null} // Không có thông tin user cho guest
    />
  );
};

export default GuestNavbar;
