import React from "react";
import "../../styles/components/Footer.scss";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { IoIosArrowForward } from "react-icons/io";
import { FaMapMarkerAlt, FaPhone } from "react-icons/fa";
import { MdEmail } from "react-icons/md";
import useHospitalInfo from "../../hooks/useHospitalInfo";
import logo from "../../assets/images/logo.png";
import { useAuth } from "../../contexts/AuthContext";

/**
 * Component Footer chung cho toàn bộ website
 * Hiển thị logo, danh mục và thông tin liên hệ bệnh viện
 */
const Footer = () => {
  // Hooks
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();

  // Lấy thông tin bệnh viện từ hook
  const { hospitalInfo, loading: hospitalLoading } = useHospitalInfo();

  /**
   * Hàm điều hướng về trang chủ và scroll đến section thông tin bệnh viện
   * Hoạt động cho cả guest và member
   */
  const handleAboutUsClick = () => {
    // Xác định trang chủ dựa trên trạng thái đăng nhập
    const homePath = user ? "/member" : "/";

    // Nếu đang ở trang chủ, chỉ cần scroll
    if (location.pathname === homePath) {
      scrollToHospitalInfo();
    } else {
      // Điều hướng về trang chủ với hash để scroll
      navigate(homePath + "#hospital-info");
    }
  };

  /**
   * Hàm scroll đến section thông tin bệnh viện
   * Được sử dụng khi đã ở trang chủ
   */
  const scrollToHospitalInfo = () => {
    const hospitalInfoSection = document.querySelector(".hospital-info-section");
    if (hospitalInfoSection) {
      hospitalInfoSection.scrollIntoView({
        behavior: "smooth",
        block: "start"
      });
    }
  };

  return (
    <footer className="footer">
      {/* Cột logo bệnh viện */}
      <div className="footer-column">
        <div className="footer-brand">
          <img src={logo} alt="Ánh Dương Logo" style={{ height: '40px', width: 'auto' }} />
        </div>
      </div>

      {/* Cột danh mục điều hướng */}
      <div className="footer-column">
        <div className="footer-title">Danh mục</div>
        <div className="footer-item">
          <IoIosArrowForward className="footer-icon" />
          <button className="footer-link" onClick={handleAboutUsClick}>
            Về chúng tôi
          </button>
        </div>
        <div className="footer-item">
          <IoIosArrowForward className="footer-icon" />
          <Link to={user ? "/member/donation-guide" : "/donation-guide"} className="footer-link">
            Hướng dẫn hiến máu
          </Link>
        </div>
      </div>

      {/* Cột thông tin liên hệ */}
      <div className="footer-column">
        <div className="footer-title">Liên hệ với chúng tôi</div>
        <div className="footer-item">
          <FaMapMarkerAlt className="footer-icon" />
          {hospitalLoading ? "Đang tải..." : hospitalInfo?.address || "đường CMT8, Q.3, TP.HCM, Vietnam"}
        </div>
        <div className="footer-item">
          <MdEmail className="footer-icon" />
          <a
            href={`mailto:${hospitalInfo?.email || "<EMAIL>"}?subject=Liên hệ về dịch vụ hiến máu&body=Xin chào,%0D%0A%0D%0ATôi muốn liên hệ về dịch vụ hiến máu.%0D%0A%0D%0AXin cảm ơn.`}
            className="footer-link"
            title="Click để gửi email"
          >
            {hospitalLoading ? "Đang tải..." : hospitalInfo?.email || "<EMAIL>"}
          </a>
        </div>
        <div className="footer-item">
          <FaPhone className="footer-icon" />
          <a
            href={`tel:${hospitalInfo?.phone || "02838554137"}`}
            className="footer-link"
            title="Click để gọi điện"
          >
            {hospitalLoading ? "Đang tải..." : hospitalInfo?.phone || "02838554137"}
          </a>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
