import { Select, Space } from "antd";
import { FaSortAmountDown } from "react-icons/fa";
import "../../styles/components/SortControls.scss";

const { Option } = Select;

const SortControls = ({
  sortOrder,
  onSortChange,
  size = "large",
  className = "",
  style = {},
}) => {
  return (
    <div className={`sort-controls ${className}`} style={style}>
      <Space align="center">
        
        <Select
          value={sortOrder}
          onChange={onSortChange}
          size={size}
          className="sort-select"
          style={{ minWidth: 150 }}
        >
          <Option value="newest"><PERSON><PERSON><PERSON> nhất</Option>
          <Option value="oldest"><PERSON><PERSON> nhất</Option>
        </Select>
      </Space>
    </div>
  );
};

export default SortControls;
