import React from "react";
import MemberNavbar from "../../components/member/MemberNavbar";
import Footer from "../../components/common/Footer";
import BloodDonationHeader from "../../components/member/BloodDonation/BloodDonationHeader";
import PersonalInfoStep from "../../components/member/BloodDonation/PersonalInfoStep";
import HealthSurveyStep from "../../components/member/BloodDonation/HealthSurveyStep";
import AppointmentStep from "../../components/member/BloodDonation/AppointmentStep";
import ResultDisplay from "../../components/member/BloodDonation/ResultDisplay";
import { useBloodDonationForm } from "../../hooks/useBloodDonationForm";
import { useHealthSurvey } from "../../hooks/useHealthSurvey";
import { useAppointmentScheduling } from "../../hooks/useAppointmentScheduling";
import { checkEligibility } from "../../utils/bloodDonationUtils";
import bloodDonationService from "../../services/bloodDonationService";
import "../../styles/pages/BloodDonationFormPage.scss";

const BloodDonationFormPage = () => {
  // Sử dụng custom hooks
  const {
    step,
    loading,
    registrationResult,
    distanceInfo,
    personalInfo,
    healthSurvey,
    appointmentData,
    currentUser,
    setStep,
    setLoading,
    setRegistrationResult,
    setHealthSurvey,
    setAppointmentData,
    resetForm,
    hasSystemDonationHistory,
  } = useBloodDonationForm();

  const {
    weightError,
    heightError,
    handleHealthSurveyChange,
    handleCheckboxChange,
    validateHealthSurveyForm,
  } = useHealthSurvey(healthSurvey, setHealthSurvey, personalInfo);

  const {
    getTimeSlotText,
    handleAppointmentSubmit,
    calculateEarliestDonationDate,
    selfReportedLastDonationDate
  } = useAppointmentScheduling(
    currentUser,
    healthSurvey,
    appointmentData,
    setLoading,
    setRegistrationResult
  );

  // Handler functions
  const handlePersonalInfoSubmit = () => {
    setStep(2);
  };

  const handleHealthSurveySubmit = async () => {
    if (!validateHealthSurveyForm()) {
      return;
    }

    setLoading(true);

    try {
      // Gửi dữ liệu chiều cao và cân nặng xuống database
      if (healthSurvey.weight || healthSurvey.height) {
        try {
          const updateData = {};
          if (healthSurvey.weight) {
            updateData.weight = parseFloat(healthSurvey.weight);
          }
          if (healthSurvey.height) {
            updateData.height = parseFloat(healthSurvey.height);
          }

          await bloodDonationService.updateUserInformation(currentUser.id, updateData);
        } catch (updateError) {
          console.error("❌ Error updating weight/height:", updateError);
        }
      }

      const eligibilityResult = checkEligibility(personalInfo, healthSurvey);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500));

      if (eligibilityResult.eligible) {
        setRegistrationResult({
          status: "success",
          message: "ĐĂNG KÝ THÀNH CÔNG",
          description: "Bạn đủ điều kiện hiến máu. Vui lòng đặt lịch hẹn.",
        });
        setStep(3);
      } else {
        setRegistrationResult({
          status: "failed",
          message: "ĐĂNG KÝ KHÔNG THÀNH CÔNG",
          description: `VÌ LÝ DO SỨC KHỎE: ${eligibilityResult.reason}`,
        });
      }
    } catch (error) {
      setRegistrationResult({
        status: "error",
        message: "LỖI HỆ THỐNG",
        description: "Có lỗi xảy ra khi xử lý đăng ký. Vui lòng thử lại.",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAppointmentChange = (field, value) => {
    setAppointmentData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleRetry = () => {
    setRegistrationResult(null);
    if (registrationResult?.status === "error") {
      setStep(3);
    } else {
      resetForm();
    }
  };

  // Show result display if there's a result
  if (
    registrationResult &&
    (registrationResult.status === "failed" ||
      registrationResult.status === "scheduled" ||
      registrationResult.status === "error")
  ) {
    return (
      <ResultDisplay
        registrationResult={registrationResult}
        appointmentData={appointmentData}
        getTimeSlotText={getTimeSlotText}
        onRetry={handleRetry}
      />
    );
  }

  return (
    <div className="blood-donation-form-page">
      <MemberNavbar />

      <div className="registration-content">
        <BloodDonationHeader currentStep={step} />

        {step === 1 && (
          <PersonalInfoStep
            personalInfo={personalInfo}
            onSubmit={handlePersonalInfoSubmit}
          />
        )}

        {step === 2 && (
          <HealthSurveyStep
            personalInfo={personalInfo}
            healthSurvey={healthSurvey}
            weightError={weightError}
            heightError={heightError}
            loading={loading}
            onHealthSurveyChange={handleHealthSurveyChange}
            onCheckboxChange={handleCheckboxChange}
            onSubmit={handleHealthSurveySubmit}
            onBack={() => setStep(1)}
            hasSystemDonationHistory={hasSystemDonationHistory}
          />
        )}

        {step === 3 && (
          <AppointmentStep
            appointmentData={appointmentData}
            distanceInfo={distanceInfo}
            loading={loading}
            onAppointmentChange={handleAppointmentChange}
            onSubmit={handleAppointmentSubmit}
            onBack={() => setStep(2)}
            getTimeSlotText={getTimeSlotText}
            healthSurvey={healthSurvey}
            calculateEarliestDonationDate={calculateEarliestDonationDate}
            selfReportedLastDonationDate={selfReportedLastDonationDate}
          />
        )}
      </div>

      <Footer />
    </div>
  );
};

export default BloodDonationFormPage;
