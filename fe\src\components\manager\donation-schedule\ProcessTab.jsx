import { <PERSON>, Card, Button, Tooltip, Space, Tag } from "antd";
import {
  EyeOutlined,
  ClockCircleOutlined,
  UserOutlined,
  CheckCircleOutlined,
  HeartOutlined,
} from "@ant-design/icons";
import FilterSection from "./FilterSection";
import { DONATION_STATUSES } from "../../shared/ProcessWorkflowModal";
import {
  formatDate,
  getStatusInfo,
  BLOOD_TYPE_OPTIONS,
  STATUS_OPTIONS,
  PROCESS_SORT_OPTIONS
} from "../../../utils/donationScheduleHelpers";

/**
 * Process tab component for donations in processing
 */
const ProcessTab = ({
  donations,
  filters,
  onFilterChange,
  processSort,
  onSortChange,
  loading,
  onViewDetails,
  onViewWorkflow,
  onStoreBlood,
  isManager,
}) => {
  const getStatusIcon = (iconName) => {
    const iconMap = {
      UserOutlined: <UserOutlined />,
      CheckCircleOutlined: <CheckCircleOutlined />,
      HeartOutlined: <HeartOutlined />,
      ClockCircleOutlined: <ClockCircleOutlined />,
    };
    return iconMap[iconName] || <UserOutlined />;
  };

  const columns = [
    {
      title: "Ngày hẹn",
      dataIndex: "appointmentDate",
      key: "appointmentDate",
      width: 130,
      sorter: true,
      render: (date) => formatDate(date),
    },
    {
      title: "Nhóm máu",
      dataIndex: "bloodType",
      key: "bloodType",
      width: 100,
      align: "center",
      sorter: true,
      render: (bloodType) => (
        <Tag color="#D93E4C" style={{ fontWeight: "bold" }}>
          {bloodType}
        </Tag>
      ),
    },
    {
      title: "Lượng máu dự kiến",
      dataIndex: "expectedQuantity",
      key: "expectedQuantity",
      width: 150,
      align: "center",
      render: (quantity) => (
        <span style={{ fontWeight: 600, color: "#20374E" }}>
          {quantity}
        </span>
      ),
    },
    {
      title: "Tên người hiến",
      dataIndex: "donorName",
      key: "donorName",
      width: 180,
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 600 }}>{text}</div>
          <div style={{ fontSize: "0.8rem", color: "#666" }}>
            ID: {record.donorId}
          </div>
        </div>
      ),
    },
    {
      title: "Trạng thái hiện tại",
      dataIndex: "status",
      key: "status",
      width: 180,
      sorter: true,
      render: (_, record) => {
        const statusInfo = getStatusInfo(record); // Pass whole record instead of just status
        return (
          <Tag
            color={statusInfo.color}
            icon={getStatusIcon(statusInfo.icon)}
            style={{ fontWeight: 600 }}
          >
            {statusInfo.text}
          </Tag>
        );
      },
    },
    {
      title: "Hành động",
      key: "actions",
      width: 220,
      render: (_, record) => (
        <Space>
          <Tooltip title="Xem chi tiết">
            <Button
              type="primary"
              icon={<EyeOutlined />}
              size="small"
              onClick={() => onViewDetails(record)}
            />
          </Tooltip>
          <Tooltip title="Quy trình hiến máu">
            <Button
              type="default"
              icon={<ClockCircleOutlined />}
              size="small"
              onClick={() => onViewWorkflow(record)}
            />
          </Tooltip>
          {isManager &&
            record.process === 4 &&
            record.status !== false && (
              <Tooltip title="Nhập kho - Cập nhật quy trình thành hoàn thành">
                <Button
                  type="default"
                  style={{
                    backgroundColor: "#52c41a",
                    borderColor: "#52c41a",
                    color: "white",
                  }}
                  size="small"
                  onClick={() => onStoreBlood(record.id)}
                >
                  Nhập kho
                </Button>
              </Tooltip>
            )}
        </Space>
      ),
    },
  ];

  const filterOptions = {
    status: {
      label: "Trạng thái:",
      options: STATUS_OPTIONS,
    },
    bloodType: {
      label: "Nhóm máu:",
      options: BLOOD_TYPE_OPTIONS,
    },
    sort: {
      label: "Sắp xếp theo:",
      options: PROCESS_SORT_OPTIONS,
    },
  };

  return (
    <div className="process-tab-content">
      <FilterSection
        filters={filters}
        onFilterChange={onFilterChange}
        sortValue={`${processSort.field}-${processSort.order}`}
        onSortChange={(value) => {
          const [field, order] = value.split("-");
          onSortChange({ field, order });
        }}
        filterOptions={filterOptions}
        showStatusFilter={true}
      />

      <Card className="process-donations-card">
        <Table
          className="donation-process-table"
          dataSource={donations}
          columns={columns}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} của ${total} quy trình`,
          }}
          scroll={{ x: 1200 }}
          onChange={(_, __, sorter) => {
            if (sorter.field) {
              onSortChange({
                field: sorter.field,
                order: sorter.order === "ascend" ? "asc" : "desc",
              });
            }
          }}
        />
      </Card>
    </div>
  );
};

export default ProcessTab;
