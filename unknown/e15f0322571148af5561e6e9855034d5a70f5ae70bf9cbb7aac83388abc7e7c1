@use '../base/_variables' as vars;
@use '../base/_mixin' as mix;

.google-callback {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: vars.$font-primary;

  &__container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    padding: 48px 32px;
    text-align: center;
    max-width: 400px;
    width: 90%;
  }

  &__content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  &__spinner {
    width: 48px;
    height: 48px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid vars.$primary-color;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  &__success-icon {
    width: 64px;
    height: 64px;
    background: #4CAF50;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 32px;
    font-weight: bold;
  }

  &__error-icon {
    width: 64px;
    height: 64px;
    background: #f44336;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 32px;
    font-weight: bold;
  }

  h2 {
    margin: 16px 0 8px 0;
    color: vars.$text-color;
    font-size: vars.$font-size-large;
    font-weight: 600;
  }

  p {
    margin: 0;
    color: vars.$text-secondary;
    font-size: vars.$font-size-base;
    line-height: 1.5;
  }

  &__redirect-info {
    font-size: vars.$font-size-small;
    color: vars.$text-secondary;
    margin-top: 16px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Responsive design
@media (max-width: 768px) {
  .google-callback {
    &__container {
      padding: 32px 24px;
      margin: 16px;
    }

    h2 {
      font-size: vars.$font-size-lg;
    }

    p {
      font-size: vars.$font-size-small;
    }
  }
}
