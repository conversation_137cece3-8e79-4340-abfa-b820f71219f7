@use "../base/variables" as vars;

.doctor-blood-requests {
  display: flex;
  min-height: 100vh;
  background: #f6f6f6;

  .doctor-blood-requests-content {
    flex: 1;
    margin-left: 0px;
    padding: 0 4px;
    font-family: "Inter", sans-serif;

    .page-header {
      margin-bottom: 24px;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      flex-wrap: wrap;
      gap: 16px;
      background: #fff;
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      border: 1px solid #e8e8e8;
      h1 {
        color: #20374e;
        margin-bottom: 8px;
        font-size: 2rem;
        font-weight: 700;
      }
      p {
        color: #666;
        font-size: 1.1rem;
        margin: 0 0 8px 0;
      }
      .auto-approve-notice {
        background: #deccaa;
        color: #20374e;
        padding: 6px 16px;
        border-radius: 16px;
        font-size: 0.95rem;
        font-weight: 600;
        border: none;
      }
    }

    .doctor-info-card {
      background: #fff;
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      margin-bottom: 24px;
      border: 1px solid #e8e8e8;
      .doctor-details {
        h3 {
          margin: 0 0 16px 0;
          color: #d93e4c;
          font-size: 1.1rem;
        }
        .info-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px solid #f0f0f0;
          &:last-child {
            border-bottom: none;
          }
          .label {
            font-weight: 600;
            color: #666;
            min-width: 100px;
          }
          .value {
            color: #20374e;
            font-weight: 500;
            &.blood-dept {
              color: #52c41a;
              font-weight: 600;
            }
            &.other-dept {
              color: #6c757d;
            }
          }
        }
      }
    }

    .tabs-section {
      margin-bottom: 24px;
      .tabs-header {
        display: flex;
        gap: 8px;
        background: #fff;
        padding: 8px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        .tab-btn {
          flex: 1;
          padding: 12px 18px;
          border: none;
          border-radius: 8px;
          background: transparent;
          color: #666;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          font-size: 1rem;
          &:hover {
            background: #f8f9fa;
            color: #d93e4c;
          }
          &.active {
            background: #d93e4c;
            color: #fff;
            box-shadow: 0 2px 8px rgba(217, 62, 76, 0.08);
          }
        }
      }
    }

    .requests-section {
      margin-bottom: 24px;
      h2 {
        color: #d93e4c;
        margin-bottom: 16px;
        font-size: 1.2rem;
        border-bottom: 2px solid #f0f0f0;
        padding-bottom: 8px;
      }
      .requests-table-container {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        border: 1px solid #e8e8e8;
        .requests-table {
          width: 100%;
          border-collapse: collapse;
          th,
          td {
            padding: 16px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
            font-family: "Inter", sans-serif;
          }
          th {
            background: #f8f9fa;
            font-weight: 600;
            color: #20374e;
            font-size: 0.95rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
          td {
            font-size: 0.95rem;
            color: #20374e;
          }
          tr:hover {
            background: #f8f9fa;
          }
          .blood-type-badge {
            display: inline-block;
            padding: 6px 25px;
            border-radius: 20px;
            font-weight: 700;
            font-size: 20px;
            text-align: center;
            min-height: 40px;
            min-width: 50px;

            &.positive {
              background-color: #e3f2fd;
              color: #1976d2;
              border: 1px solid #bbdefb;
            }

            &.negative {
              background-color: #ffebee;
              color: #c62828;
              border: 1px solid #ffcdd2;
            }
          }
        }
      }
    }

    .statistics-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1.5rem;

      .stat-card {
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        text-align: center;

        h3 {
          margin: 0 0 1rem 0;
          color: #666;
          font-size: 0.9rem;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .stat-number {
          font-size: 2rem;
          font-weight: 700;
          margin: 0;
          color: #28a745;

          &.warning {
            color: #ffc107;
          }

          &.success {
            color: #28a745;
          }

          &.danger {
            color: #dc3545;
          }
        }
      }
    }
  }
}

// Modal styles
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;

  .modal-content {
    background: white;
    border-radius: 12px;
    max-width: 700px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);

    .modal-header {
      padding: 1.5rem;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;
      align-items: center;

      h2 {
        margin: 0;
        color: #28a745;
      }

      .close-btn {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: #666;

        &:hover {
          color: #333;
        }
      }
    }

    .modal-body {
      padding: 1.5rem;

      .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 1rem;
      }

      .form-group {
        margin-bottom: 1rem;

        label {
          display: block;
          margin-bottom: 0.5rem;
          font-weight: 600;
          color: #333;
        }

        select,
        input,
        textarea {
          width: 100%;
          padding: 0.5rem;
          border: 1px solid #ddd;
          border-radius: 6px;
          font-size: 0.9rem;

          &:focus {
            outline: none;
            border-color: #28a745;
            box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
          }
        }

        textarea {
          resize: vertical;
          min-height: 80px;
        }
      }

      .auto-approve-info {
        background: #d1ecf1;
        color: #0c5460;
        padding: 1rem;
        border-radius: 6px;
        margin-bottom: 1rem;
        font-size: 0.9rem;
        border: 1px solid #bee5eb;
      }

      .modal-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;

        .btn {
          padding: 0.5rem 1rem;
          border: none;
          border-radius: 6px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;

          &.btn-secondary {
            background: #6c757d;
            color: white;

            &:hover {
              background: #5a6268;
            }
          }

          &.btn-primary {
            background: #28a745;
            color: white;

            &:hover {
              background: #218838;
            }

            &:disabled {
              opacity: 0.6;
              cursor: not-allowed;
            }
          }
        }
      }
    }
  }
}

// Responsive
@media (max-width: 768px) {
  .doctor-blood-requests {
    .doctor-blood-requests-content {
      margin-left: 0;
      padding: 1rem;

      .page-header {
        flex-direction: column;
        align-items: stretch;
      }

      .doctor-info-card .doctor-details .info-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
      }

      .requests-section .requests-table-container {
        overflow-x: auto;

        .requests-table {
          min-width: 800px;
        }
      }

      .statistics-section {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;

        .stat-card {
          padding: 1rem;

          .stat-number {
            font-size: 1.5rem;
          }
        }
      }
    }
  }

  .modal-overlay .modal-content .modal-body .form-row {
    grid-template-columns: 1fr;
  }
}

// Action buttons styling
.doctor-blood-requests
  .doctor-blood-requests-content
  .requests-section
  .requests-table-container
  .requests-table {
  .action-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    justify-content: center;

    .btn {
      &.btn-success {
        background: #28a745;
        color: white;

        &:hover {
          background: #218838;
          transform: translateY(-1px);
        }
      }

      &.btn-danger {
        background: #dc3545;
        color: white;

        &:hover {
          background: #c82333;
          transform: translateY(-1px);
        }
      }
    }
  }
}
