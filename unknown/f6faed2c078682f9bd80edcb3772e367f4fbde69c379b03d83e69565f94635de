import React from "react";
import { Card, Spin, Typography } from "antd";

const { Title, Paragraph } = Typography;

/**
 * Component Loading State cho ActivityHistoryPage
 * G<PERSON><PERSON> nguyên thiết kế từ file gốc
 */
const ActivityLoadingState = () => {
  return (
    <Card className="loading-state">
      <div className="loading-content">
        <Spin size="large" className="loading-spinner" />
        <div className="loading-text-wrapper">
          <Title level={3} className="loading-title">
            🔄 Đang tải dữ liệu...
          </Title>
          <Paragraph className="loading-description">
            <PERSON>ui lòng chờ trong giây lát để tải thông tin hoạt động của bạn
          </Paragraph>
        </div>
      </div>
    </Card>
  );
};

export default ActivityLoadingState;
