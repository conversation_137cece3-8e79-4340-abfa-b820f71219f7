.address-form {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e9ecef;
  margin-bottom: 1.5rem;

  .address-fields {
    // New address inputs section
    .address-inputs {
      margin-bottom: 1.5rem;

      .input-row {
        display: flex;
        gap: 1rem;
        margin-bottom: 1rem;

        @media (max-width: 768px) {
          flex-direction: column;
          gap: 0.5rem;
        }

        .input-group {
          flex: 1;

          label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #495057;
            font-size: 0.9rem;

            .required {
              color: #dc3545;
              margin-left: 0.25rem;
            }
          }

          input,
          select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;

            &:focus {
              outline: none;
              border-color: #17a2b8;
              box-shadow: 0 0 0 3px rgba(23, 162, 184, 0.1);
              transform: translateY(-1px);
            }

            &:disabled {
              background: #f8f9fa;
              cursor: not-allowed;
              opacity: 0.7;
            }

            &::placeholder {
              color: #adb5bd;
              font-style: italic;
            }
          }

          select {
            cursor: pointer;

            option {
              padding: 0.5rem;
              background: white;
              color: #495057;
            }

            &:disabled {
              cursor: not-allowed;
            }
          }
        }
      }
    }

    .field-row {
      display: flex;
      gap: 1rem;
      margin-bottom: 1rem;

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 0.5rem;
      }

      .form-group {
        flex: 1;

        &.full-width {
          flex: 1;
        }

        label {
          display: block;
          margin-bottom: 0.5rem;
          font-weight: 600;
          color: #495057;
          font-size: 0.9rem;

          .required {
            color: #dc3545;
            margin-left: 0.25rem;
          }
        }

        .form-input,
        .form-select {
          width: 100%;
          padding: 0.75rem;
          border: 2px solid #e9ecef;
          border-radius: 8px;
          font-size: 1rem;
          transition: all 0.3s ease;
          background: white;

          &:focus {
            outline: none;
            border-color: #17a2b8;
            box-shadow: 0 0 0 3px rgba(23, 162, 184, 0.1);
            transform: translateY(-1px);
          }

          &:disabled {
            background: #f8f9fa;
            cursor: not-allowed;
            opacity: 0.7;
          }

          &::placeholder {
            color: #adb5bd;
            font-style: italic;
          }
        }

        .form-select {
          cursor: pointer;

          option {
            padding: 0.5rem;
            background: white;
            color: #495057;
          }

          &:disabled {
            cursor: not-allowed;
          }
        }
      }
    }

    .full-address-preview {
      margin-top: 1.5rem;
      padding-top: 1.5rem;
      border-top: 1px solid #e9ecef;

      label {
        display: block;
        margin-bottom: 0.75rem;
        font-weight: 600;
        color: #495057;
        font-size: 1rem;
      }

      .address-display {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border: 2px solid #dee2e6;
        border-radius: 10px;
        padding: 1rem;
        font-size: 1rem;
        color: #495057;
        min-height: 50px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        transition: all 0.3s ease;

        &.geocoding {
          border-color: #ffc107;
          background: linear-gradient(135deg, #fff3cd, #ffeaa7);
        }

        .geocoding-indicator {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          color: #856404;
          font-size: 0.9rem;
          font-weight: 600;

          .loading-spinner {
            width: 16px;
            height: 16px;
            border: 2px solid #ffeaa7;
            border-top: 2px solid #856404;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }
        }
      }
    }

    .address-help {
      margin-top: 0.5rem;
      padding: 0.5rem;
      background: linear-gradient(135deg, #e3f2fd, #bbdefb);
      border-radius: 6px;
      border-left: 3px solid #2196f3;

      small {
        color: #1565c0;
        font-weight: 500;
      }
    }

    .error-message {
      margin-top: 1rem;
      padding: 0.75rem;
      background: linear-gradient(135deg, #f8d7da, #f5c6cb);
      color: #721c24;
      border-radius: 8px;
      border: 1px solid #f5c6cb;
      font-weight: 600;

      .error-help {
        margin-top: 0.5rem;
        padding-top: 0.5rem;
        border-top: 1px solid rgba(113, 28, 36, 0.2);

        small {
          color: #5a1a1f;
          font-weight: 400;
          font-style: italic;
        }
      }
    }
  }

  .distance-info-section {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 2px solid #e9ecef;

    h4 {
      margin: 0 0 1.5rem 0;
      background: linear-gradient(45deg, #17a2b8, #20c997);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-size: 1.2rem;
      font-weight: 700;
    }

    .distance-details {
      background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
      border-radius: 10px;
      padding: 1rem;
      margin-bottom: 1.5rem;

      .distance-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.5);

        &:last-child {
          border-bottom: none;
        }

        .distance-label {
          font-weight: 600;
          color: #495057;
        }

        .distance-value {
          font-weight: 700;
          font-size: 1.1rem;
        }
      }
    }

    .hospital-info {
      background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
      border: 1px solid #c3e6c3;
      border-radius: 10px;
      padding: 1rem;

      h5 {
        margin: 0 0 1rem 0;
        color: #2e7d32;
        font-size: 1.1rem;
        font-weight: 700;
      }

      .hospital-details {
        margin-bottom: 1rem;

        .hospital-item {
          margin-bottom: 0.5rem;
          color: #495057;
          font-weight: 500;

          &:first-child {
            color: #2e7d32;
            font-weight: 700;
            font-size: 1.05rem;
          }
        }
      }

      .directions-links {
        display: flex;
        gap: 0.75rem;
        flex-wrap: wrap;

        .directions-link {
          display: inline-block;
          color: white;
          text-decoration: none;
          padding: 0.5rem 1rem;
          border-radius: 8px;
          font-weight: 600;
          font-size: 0.9rem;
          transition: all 0.3s ease;

          &.osm-link {
            background: linear-gradient(45deg, #17a2b8, #20c997);

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
              text-decoration: none;
              color: white;
            }
          }

          &.google-link {
            background: linear-gradient(45deg, #dc3545, #fd7e14);

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
              text-decoration: none;
              color: white;
            }
          }
        }
      }
    }
  }
}

// Animations
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Responsive
@media (max-width: 768px) {
  .address-form {
    padding: 1rem;

    .distance-info-section {
      .distance-details .distance-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
      }
    }
  }
}
