import React, { useState } from "react";
import { Link } from "react-router-dom";
import { validateEmail } from "../../utils/validation";
import "../../styles/components/ForgotPasswordForm.scss";
import authService from "../../services/authService";

export default function ForgotPasswordForm() {
  const [email, setEmail] = useState("");
  const [error, setError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);


  const handleInputChange = (e) => {
    setEmail(e.target.value);
    setError("");
    setSuccessMessage("");
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!email) {
      setError("Vui lòng nhập địa chỉ email");
      return;
    }

    if (!validateEmail(email)) {
      setError("<PERSON>ail không đúng định dạng");
      return;
    }

    setIsLoading(true);
    setError("");
    setSuccessMessage("");

    try {
      const result = await authService.forgotPassword(email);

      if (result.success) {
        setSuccessMessage("Đã gửi email đặt lại mật khẩu. Vui lòng kiểm tra hộp thư của bạn và nhấp vào liên kết trong email để đặt lại mật khẩu.");
        // Don't automatically redirect - let user check email and click the reset link
      } else {
        setError(result.error || "Không thể gửi email đặt lại mật khẩu. Vui lòng thử lại.");
      }
    } catch (error) {
      console.error("Forgot password failed:", error);
      setError("Đã xảy ra lỗi hệ thống. Vui lòng thử lại sau.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="forgot-password-form__container">
      <div className="forgot-password-form__box">
        <div className="forgot-password-form__logo">LOGO</div>
        <div className="forgot-password-form__title">
          QUÊN MẬT KHẨU
        </div>
        <div className="forgot-password-form__description">
          Nhập địa chỉ email của bạn và chúng tôi sẽ gửi cho bạn liên kết để đặt lại mật khẩu.
        </div>

        <form className="forgot-password-form__form" onSubmit={handleSubmit}>
          <label className="forgot-password-form__label">EMAIL</label>
          <input
            className={`forgot-password-form__input ${error ? "error" : ""}`}
            type="email"
            name="email"
            value={email}
            onChange={handleInputChange}
            placeholder="Nhập địa chỉ email"
            required
          />

          {successMessage && (
            <div className="forgot-password-form__success">{successMessage}</div>
          )}
          {error && (
            <div className="forgot-password-form__error">{error}</div>
          )}

          <button
            className="forgot-password-form__submit"
            type="submit"
            disabled={isLoading}
          >
            {isLoading ? "ĐANG GỬI..." : "GỬI EMAIL ĐẶT LẠI"}
          </button>
        </form>

        <div className="forgot-password-form__back">
          <Link to="/login">
            ← Quay lại đăng nhập
          </Link>
        </div>
      </div>
    </div>
  );
}
