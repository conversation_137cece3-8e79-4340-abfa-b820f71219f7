@use "../base/variables" as vars;
@use "../base/mixin" as mix;

.profile-page {
  padding: vars.$spacing-lg;
  background: vars.$background-light;
  min-height: 100vh;

  .profile-update-container {
    max-width: 1200px;
    margin: 0 auto;

    .ant-typography {
      color: vars.$text-color;
      
      h2 {
        color: vars.$primary-color;
        font-weight: 600;
        margin-bottom: vars.$spacing-lg;
      }
    }

    .ant-card {
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      border: 1px solid vars.$border-color;
      
      .ant-card-head {
        border-bottom: 1px solid vars.$border-color;
        background: vars.$background-main;
        border-radius: 12px 12px 0 0;
        
        .ant-card-head-title {
          color: vars.$text-color;
          font-weight: 600;
          font-size: 16px;
          
          .anticon {
            color: vars.$primary-color;
            margin-right: 8px;
          }
        }
      }

      .ant-card-body {
        padding: vars.$spacing-lg;
      }
    }

    .ant-form {
      .ant-form-item-label {
        > label {
          color: vars.$text-color;
          font-weight: 500;
          font-size: 14px;
        }
      }

      .ant-input,
      .ant-input-password {
        border-radius: 8px;
        border: 1px solid vars.$border-color;
        
        &:hover {
          border-color: vars.$primary-color;
        }
        
        &:focus,
        &.ant-input-focused {
          border-color: vars.$primary-color;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .ant-input-prefix {
          color: vars.$text-muted;
        }
      }

      .ant-input-password {
        .ant-input-suffix {
          .anticon {
            color: vars.$text-muted;
            
            &:hover {
              color: vars.$primary-color;
            }
          }
        }
      }

      .ant-btn {
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        &:active {
          transform: translateY(0);
        }
      }
    }
  }

  // Responsive design
  @include mix.tablet {
    padding: vars.$spacing-md;
    
    .profile-update-container {
      .ant-card {
        .ant-card-body {
          padding: vars.$spacing-md;
        }
      }
    }
  }

  @include mix.mobile {
    padding: vars.$spacing-sm;
    
    .profile-update-container {
      .ant-typography {
        h2 {
          font-size: 20px;
          text-align: center;
        }
      }
      
      .ant-card {
        margin-bottom: vars.$spacing-md;
        
        .ant-card-head {
          .ant-card-head-title {
            font-size: 14px;
          }
        }
        
        .ant-card-body {
          padding: vars.$spacing-sm;
        }
      }
      
      .ant-form {
        .ant-form-item {
          margin-bottom: vars.$spacing-md;
        }
        
        .ant-btn {
          height: 44px;
          font-size: 15px;
        }
      }
    }
  }
}

// Loading state
.profile-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  
  .ant-spin {
    margin-bottom: vars.$spacing-md;
  }
  
  p {
    color: vars.$text-muted;
    font-size: 14px;
  }
}

// Success message styling
.ant-message {
  .ant-message-notice {
    .ant-message-notice-content {
      .ant-message-success {
        .anticon {
          color: #52c41a;
        }
      }
      
      .ant-message-error {
        .anticon {
          color: #ff4d4f;
        }
      }
    }
  }
}
