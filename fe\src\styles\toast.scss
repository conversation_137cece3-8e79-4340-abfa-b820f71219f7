// Toast Styles - Modern Version
.toast-container {
  position: fixed;
  top: 24px;
  right: 24px;
  z-index: 1050;

  .toast {
    min-width: 300px;
    max-width: 400px;
    margin-bottom: 14px;
    border: 1px solid transparent;
    border-radius: 14px;
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.08);
    background-color: #ffffff;
    overflow: hidden;
    animation: fadeSlideIn 0.35s ease-in-out;

    .toast-header {
      display: flex;
      align-items: center;
      background-color: transparent;
      font-weight: 600;
      border-bottom: none;
      font-size: 14px;
      padding: 10px 16px;

      .icon {
        font-size: 16px;
        margin-right: 10px;
      }

      .btn-close {
        margin-left: auto;
        background: transparent;
        opacity: 0.5;

        &:hover {
          opacity: 0.9;
        }
      }
    }

    .toast-body {
      padding: 12px 16px;
      font-size: 14px;
      color: #333;
      line-height: 1.5;
    }

    // Status Variants
    &.toast-success {
      border-left: 4px solid #52c41a;

      .toast-header {
        color: #2f8a0c;

        .icon {
          color: #52c41a;
        }
      }
    }

    &.toast-error {
      border-left: 4px solid #ff4d4f;

      .toast-header {
        color: #b71c1c;

        .icon {
          color: #ff4d4f;
        }
      }
    }

    &.toast-warning {
      border-left: 4px solid #faad14;

      .toast-header {
        color: #ad6800;

        .icon {
          color: #faad14;
        }
      }
    }

    &.toast-info {
      border-left: 4px solid #1890ff;

      .toast-header {
        color: #0050b3;

        .icon {
          color: #1890ff;
        }
      }
    }

    &.toast-loading {
      border-left: 4px solid #722ed1;

      .toast-header {
        color: #391085;

        .icon {
          color: #9254de;
        }
      }

      .loading-spinner {
        width: 16px;
        height: 16px;
        border: 2px solid rgba(0, 0, 0, 0.1);
        border-top: 2px solid #9254de;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }
}

// Animations
@keyframes fadeSlideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Responsive
@media (max-width: 768px) {
  .toast-container {
    top: 12px;
    left: 12px;
    right: 12px;

    .toast {
      width: 100%;
      min-width: auto;
      max-width: none;
    }
  }
}
