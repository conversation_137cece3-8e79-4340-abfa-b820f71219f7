import React from "react";
import { Card, Row, Col, Typography } from "antd";
import {
  HeartFilled,
  TrophyOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
} from "@ant-design/icons";

const { Title, Text } = Typography;

/**
 * Component Statistics Overview cho ActivityHistoryPage
 * Giữ nguyên thiết kế và logic từ file gốc
 */
const ActivityStatisticsOverview = ({
  activities,
  donationCount,
  requestCount,
  completedCount,
}) => {
  return (
    <Card className="statistics-overview-card">
      <div className="overview-header">
        <Title level={3} className="overview-title">
          Tổng quan hoạt động
        </Title>
        <Text className="overview-subtitle">
          Thống kê chi tiết các hoạt động hiến máu và yêu cầu máu của bạn
        </Text>
      </div>

      <Row gutter={[24, 24]} className="statistics-grid">
        <Col xs={24} sm={12} lg={6}>
          <div className="statistic-item total-activities">
            <div className="statistic-icon-wrapper">
              <div className="statistic-icon">
                <TrophyOutlined />
              </div>
            </div>
            <div className="statistic-content">
              <div className="statistic-number">{activities.length}</div>
              <div className="statistic-label">Tổng hoạt động</div>
              <div className="statistic-description">
                Tất cả lịch sử của bạn
              </div>
            </div>
          </div>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <div className="statistic-item donations">
            <div className="statistic-icon-wrapper">
              <div className="statistic-icon">
                <HeartFilled />
              </div>
            </div>
            <div className="statistic-content">
              <div className="statistic-number">{donationCount}</div>
              <div className="statistic-label">Lần hiến máu</div>
            </div>
          </div>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <div className="statistic-item requests">
            <div className="statistic-icon-wrapper">
              <div className="statistic-icon">
                <FileTextOutlined />
              </div>
            </div>
            <div className="statistic-content">
              <div className="statistic-number">{requestCount}</div>
              <div className="statistic-label">Yêu cầu máu</div>
            </div>
          </div>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <div className="statistic-item completed">
            <div className="statistic-icon-wrapper">
              <div className="statistic-icon">
                <CheckCircleOutlined />
              </div>
            </div>
            <div className="statistic-content">
              <div className="statistic-number">{completedCount}</div>
              <div className="statistic-label">Hoàn thành</div>
            </div>
          </div>
        </Col>
      </Row>
    </Card>
  );
};

export default ActivityStatisticsOverview;
