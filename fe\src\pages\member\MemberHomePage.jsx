import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import GuestHomePage from "../guest/GuestHomePage";
import MemberNavbar from "../../components/member/MemberNavbar";
import UnifiedModal from "../../components/member/UnifiedModal";
import authService from "../../services/authService";
import userInfoService from "../../services/userInfoService";
import { getUserName } from "../../utils/userUtils";
import useBloodRequestCheck from "../../hooks/useBloodRequestCheck";
import blood1 from "../../assets/images/blood1.jpg";
import "../../styles/pages/MemberHomePage.scss";

/**
 * Trang chủ dành cho thành viên đã đăng nhập
 * Kế thừa layout từ GuestHomePage nhưng có navbar và hero section riêng
 * <PERSON><PERSON> gồm các chức năng kiểm tra hồ sơ và yêu cầu hiến máu
 */
const MemberHomePage = () => {
  // === HOOKS ===
  const navigate = useNavigate();
  const location = useLocation();
  const user = authService.getCurrentUser(); // Thông tin người dùng hiện tại
  const userName = getUserName(); // Tên hiển thị của người dùng

  // Hook kiểm tra yêu cầu hiến máu đang chờ
  const { isChecking, pendingRequest, checkPendingRequest } =
    useBloodRequestCheck();

  // === STATE MANAGEMENT ===
  const [showModal, setShowModal] = useState(false); // Hiển thị modal
  const [modalType, setModalType] = useState("profile-incomplete"); // Loại modal: 'profile-incomplete' hoặc 'blood-request-status'
  const [modalContext, setModalContext] = useState("general"); // Ngữ cảnh modal: 'donation', 'request', hoặc 'general'
  const [profileCheckResult, setProfileCheckResult] = useState(null); // Kết quả kiểm tra hồ sơ

  // === EFFECTS ===
  // Xử lý scroll đến section khi có hash trong URL
  useEffect(() => {
    if (location.hash) {
      const elementId = location.hash.substring(1); // Bỏ dấu #
      const element = document.getElementById(elementId);
      if (element) {
        // Delay để đảm bảo page đã render xong
        setTimeout(() => {
          element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }, 500);
      }
    }
  }, [location.hash]);

  // === EVENT HANDLERS ===
  /**
   * Xử lý click nút đăng ký hiến máu
   * Kiểm tra tính đầy đủ của hồ sơ trước khi cho phép đăng ký
   * @param {Event} e - Event click
   */
  const handleBloodDonationClick = async (e) => {
    e.preventDefault();

    try {
      // Kiểm tra hồ sơ có đầy đủ thông tin không
      const userInfo = await userInfoService.getUserInfo(user.id);
      const profileCheck = userInfoService.checkProfileCompleteness(userInfo);

      if (!profileCheck.isComplete) {
        // Hồ sơ chưa đầy đủ - hiển thị modal với danh sách thông tin còn thiếu
        setProfileCheckResult(profileCheck);
        setModalType("profile-incomplete");
        setModalContext("donation");
        setShowModal(true);
        return;
      }

      // Hồ sơ đầy đủ - chuyển đến form đăng ký hiến máu
      navigate("/member/blood-donation-form");
    } catch (error) {
      // Nếu có lỗi khi kiểm tra hồ sơ, vẫn cho phép điều hướng
      navigate("/member/blood-donation-form");
    }
  };

  /**
   * Xử lý click nút đăng ký nhận máu
   * Kiểm tra tính đầy đủ của hồ sơ và yêu cầu đang chờ xử lý
   * @param {Event} e - Event click
   */
  const handleBloodRequestClick = async (e) => {
    e.preventDefault();

    try {
      // Đầu tiên kiểm tra hồ sơ có đầy đủ thông tin không
      const userInfo = await userInfoService.getUserInfo(user.id);
      const profileCheck = userInfoService.checkProfileCompleteness(userInfo);

      if (!profileCheck.isComplete) {
        // Hồ sơ chưa đầy đủ - hiển thị modal với danh sách thông tin còn thiếu
        setProfileCheckResult(profileCheck);
        setModalType("profile-incomplete");
        setModalContext("request");
        setShowModal(true);
        return;
      }

      // Hồ sơ đầy đủ, kiểm tra có yêu cầu đang chờ xử lý không
      const result = await checkPendingRequest();

      if (result.success) {
        if (result.hasPendingRequest) {
          // Có yêu cầu đang chờ - hiển thị modal thông báo
          setModalType("blood-request-status");
          setModalContext("request");
          setShowModal(true);
        } else {
          // Không có yêu cầu đang chờ - chuyển đến form đăng ký
          navigate("/member/blood-request-form");
        }
      } else {
        // Lỗi khi kiểm tra - vẫn cho phép điều hướng
        navigate("/member/blood-request-form");
      }
    } catch (error) {
      // Nếu có lỗi khi kiểm tra hồ sơ, vẫn cho phép điều hướng
      navigate("/member/blood-request-form");
    }
  };

  // === HELPER FUNCTIONS ===
  /**
   * Lấy thông tin nhóm máu từ hồ sơ cá nhân
   * Ưu tiên lấy từ user profile, sau đó từ localStorage
   * @returns {string} Thông tin nhóm máu (VD: "A-Rh+", "O-Rh-", "Chưa xác định")
   */
  const getBloodTypeInfo = () => {
    // Thử lấy từ localStorage trước
    const storedInfo = JSON.parse(localStorage.getItem("memberInfo") || "{}");

    // Lấy từ user profile hoặc storedInfo
    const bloodGroup = user?.profile?.bloodGroup || storedInfo.bloodGroup || "";
    const rhType = user?.profile?.rhType || storedInfo.rhType || "";

    // Nếu có cả bloodGroup và rhType thì kết hợp lại
    if (bloodGroup && rhType) {
      return `${bloodGroup}-${rhType}`;
    }
    // Nếu chỉ có bloodGroup
    else if (bloodGroup) {
      return bloodGroup;
    }
    // Nếu không có thông tin
    else {
      return "Chưa xác định";
    }
  };

  // === COMPONENTS ===
  /**
   * Hero section tùy chỉnh dành cho thành viên
   * Hiển thị lời chào cá nhân hóa và các nút hành động
   */
  const MemberHeroSection = () => (
    <section
      className="hero-section member-hero"
      data-aos="fade-up"
      style={{
        backgroundImage: `url(${blood1})`,
        backgroundRepeat: "no-repeat",
        backgroundPosition: "center center",
        backgroundSize: "cover",
      }}
    >
      <div className="hero-container">
        <div className="hero-content">
          <h1 className="merriweather-title">
            CHÀO MỪNG, {userName.toUpperCase()}
            <br />
            CÙNG CHIA SẺ YÊU THƯƠNG
          </h1>
          <p className="merriweather-content">
            Cảm ơn bạn đã tham gia cộng đồng hiến máu. Hãy chọn hành động phù
            hợp để góp phần cứu sống những sinh mạng quý giá.
          </p>

          {/* Các nút hành động chính */}
          <div className="cta-row">
            <button
              onClick={handleBloodDonationClick}
              className="cta-button"
              disabled={isChecking}
            >
              {isChecking ? "ĐANG KIỂM TRA..." : "ĐĂNG KÝ HIẾN MÁU"}
            </button>
            <button
              onClick={handleBloodRequestClick}
              className="cta-button secondary"
              disabled={isChecking}
            >
              {isChecking ? "ĐANG KIỂM TRA..." : "ĐĂNG KÝ NHẬN MÁU"}
            </button>
          </div>

          {/* Thông tin nhóm máu của thành viên */}
          <div className="member-info">
            <div className="info-item" style={{ display: "flex", alignItems: "center", gap: "12px" }}>
              <span className="label">Nhóm máu:</span>
              <span
                className="blood-type-badge"
                style={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  textAlign: "center",
                  minWidth: "80px",
                  height: "20px",
                  fontWeight: "bold",
                  fontSize: "1.2rem",
                }}
              >
                {getBloodTypeInfo()}
              </span>
            </div>
          </div>
        </div>
        <div className="hero-image">
          <img src={blood1} alt="Truyền máu" className="hero-img" />
        </div>
      </div>
    </section>
  );

  // === RENDER ===
  return (
    <>
      {/* Sử dụng layout của GuestHomePage với navbar và hero section tùy chỉnh */}
      <GuestHomePage
        CustomNavbar={MemberNavbar}
        CustomHeroSection={MemberHeroSection}
      />

      {/* Modal thống nhất cho các thông báo */}
      <UnifiedModal
        visible={showModal}
        onClose={() => setShowModal(false)}
        type={modalType}
        context={modalContext}
        profileCheckResult={profileCheckResult}
        pendingRequest={pendingRequest}
        onGoToProfile={() => {
          setShowModal(false);
          navigate("/member/profile");
        }}
        onViewHistory={() => {
          setShowModal(false);
          navigate("/member/activity-history");
        }}
      />
    </>
  );
};

export default MemberHomePage;
