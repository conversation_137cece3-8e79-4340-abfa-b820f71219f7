import React from "react";
import { Card, Row, Col, Select, Space, Typography, Spin, Input, DatePicker } from "antd";
import {
  FilterOutlined,
  HeartOutlined,
  MedicineBoxOutlined,
  LoadingOutlined,
  SearchOutlined,
  CalendarOutlined,
} from "@ant-design/icons";

const { Option } = Select;
const { Text } = Typography;
const { RangePicker } = DatePicker;

const ActivityFilterSection = ({
  filter,
  onFilterChange,
  activities,
  donationCount,
  requestCount,
  loading = false,
  searchId = "",
  onSearchIdChange,
  dateRange = null,
  onDateRangeChange,
}) => {
  return (
    <Card className="filter-section-card">
      <Row align="middle" gutter={16} wrap>
        <Col xs={24} sm={12} md={8}>
          <Space align="center">
            <div className="filter-icon-wrapper">
              <FilterOutlined className="filter-icon" />
            </div>
            <Text strong className="filter-label">
              Lọc theo loại:
            </Text>
          </Space>
        </Col>
        <Col xs={24} sm={12} md={8}>
          <Select
            value={filter}
            onChange={onFilterChange}
            size="large"
            placeholder="Chọn loại hoạt động"
            className="filter-select"
            loading={loading}
            suffixIcon={loading ? <LoadingOutlined spin /> : undefined}
            style={{ width: '100%' }}
          >
            <Option value="all">
              <Space>
               
                <span>Tất cả ({activities.length})</span>
              </Space>
            </Option>
            <Option value="donations">
              <Space>
                <HeartOutlined style={{ color: "#d32f2f" }} />
                <span>Hiến máu ({donationCount})</span>
              </Space>
            </Option>
            <Option value="requests">
              <Space>
                <MedicineBoxOutlined style={{ color: "#1890ff" }} />
                <span>Yêu cầu máu ({requestCount})</span>
              </Space>
            </Option>
          </Select>
        </Col>

        {/* Date Range Filter */}
        <Col xs={24} sm={12} md={8}>
          <Space align="center" style={{ width: '100%' }}>
            <CalendarOutlined className="filter-icon" />
            <Text strong className="filter-label">
              Thời gian:
            </Text>
          </Space>
          <RangePicker
            value={dateRange}
            onChange={onDateRangeChange}
            size="large"
            placeholder={['Từ ngày', 'Đến ngày']}
            style={{ width: '100%', marginTop: 8 }}
            format="DD/MM/YYYY"
          />
        </Col>

      </Row>
    </Card>
  );
};

export default ActivityFilterSection;
