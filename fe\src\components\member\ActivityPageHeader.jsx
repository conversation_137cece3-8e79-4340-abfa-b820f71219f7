import React from "react";
import { <PERSON>, <PERSON>, Col, Space, Typography, Breadcrumb, But<PERSON>, Spin } from "antd";
import {
  HeartFilled,
  ReloadOutlined,
  HomeOutlined,
  UserOutlined,
  HistoryOutlined,
  BarChartOutlined,
} from "@ant-design/icons";

const { Title, Paragraph } = Typography;

/**
 * Component Header cho ActivityHistoryPage
 * Giữ nguyên thiết kế và logic từ file gốc
 */
const ActivityPageHeader = ({ loading, onReload }) => {
  return (
    <Card className="page-header-card">
      <Row justify="space-between" align="middle">
        <Col>
          <Space direction="vertical" size={4}>
            <Title level={1} className="page-title">
              <HeartFilled className="heart-icon" />
              Lịch sử hoạt động
            </Title>
            <Paragraph className="page-description" style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <BarChartOutlined style={{ color: '#1890ff' }} /> <PERSON><PERSON><PERSON> lịch sử hiến máu và yêu cầu máu của bạn
            </Paragraph>
            <Breadcrumb
              className="breadcrumb-wrapper"
              separator="›"
              items={[
                {
                  title: (
                    <span className="breadcrumb-item inactive">
                      <HomeOutlined className="breadcrumb-icon" /> Trang chủ
                    </span>
                  ),
                },
                {
                  title: (
                    <span className="breadcrumb-item inactive">
                      <UserOutlined className="breadcrumb-icon" /> Thành viên
                    </span>
                  ),
                },
                {
                  title: (
                    <span className="breadcrumb-item active">
                      <Space>
                        <HistoryOutlined />
                        <span>Lịch sử hoạt động</span>
                      </Space>
                    </span>
                  ),
                },
              ]}
            />
          </Space>
        </Col>
        <Col>
          <Button
            type="primary"
            size="large"
            icon={loading ? <Spin size="small" /> : <ReloadOutlined />}
            onClick={onReload}
            disabled={loading}
            className="refresh-button"
          >
            {loading ? "Đang tải..." : "Làm mới"}
          </Button>
        </Col>
      </Row>
    </Card>
  );
};

export default ActivityPageHeader;
