import React, { useState, useEffect, useRef } from "react";
import logoImg from "../../assets/images/logo.jpg";
import { Link, useLocation, useNavigate } from "react-router-dom";
import authService from "../../services/authService";
import { useNotification } from "../../contexts/NotificationContext";
import { getUserName } from "../../utils/userUtils";
import "../../styles/components/MemberNavbar.scss";

/**
 * <PERSON>h sách các mục điều hướng chính cho thành viên
 */
const navItems = [
  { path: "/member", label: "Trang chủ" },
  { path: "/member/blood-info", label: "Tài liệu" },
  { path: "/member/blog", label: "Tin tức" },
  { path: "/member/donation-guide", label: "Hướng dẫn hiến máu" },
];

/**
 * Navbar dành cho thành viên đã đăng nhập
 * Bao gồm menu điều hướng, thông báo và dropdown user
 */
const MemberNavbar = () => {
  // === HOOKS ===
  const location = useLocation();
  const navigate = useNavigate();
  const { unreadCount } = useNotification(); // Context quản lý thông báo real-time
  const userName = getUserName(); // Tên hiển thị của người dùng

  // === STATE MANAGEMENT ===
  const [showMenu, setShowMenu] = useState(false); // Hiển thị dropdown menu desktop
  const [showMobileNav, setShowMobileNav] = useState(false); // Hiển thị menu mobile

  // === REFS ===
  const dropdownRef = useRef(null); // Ref cho dropdown menu
  const mobileNavRef = useRef(null); // Ref cho mobile nav

  // === EFFECTS ===
  /**
   * Đóng dropdown và mobile nav khi click bên ngoài
   */
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowMenu(false);
      }
      if (mobileNavRef.current && !mobileNavRef.current.contains(event.target)) {
        setShowMobileNav(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // === EVENT HANDLERS ===
  /**
   * Xử lý đăng xuất
   * Gọi API logout và chuyển hướng về trang chủ
   */
  const handleLogout = async () => {
    try {
      await authService.logout();
      navigate("/");
    } catch (error) {
      // Vẫn chuyển hướng ngay cả khi API logout lỗi
      navigate("/");
    }
  };

  /**
   * Đóng dropdown menu khi click vào item
   */
  const handleMenuItemClick = () => {
    setShowMenu(false);
  };

  /**
   * Đóng mobile nav khi click vào item
   */
  const handleMobileNavItemClick = () => {
    setShowMobileNav(false);
  };

  /**
   * Toggle hiển thị mobile nav
   */
  const toggleMobileNav = () => {
    setShowMobileNav(prev => !prev);
  };

  // === RENDER ===
  return (
    <header className="navbar member-navbar">
      {/* Logo */}
      <div className="navbar-logo">
        <Link to="/member">
          <img src={logoImg} alt="Blood Donation Logo" className="logo-img" />
        </Link>
      </div>

      {/* Navigation menu cho desktop */}
      <nav className="navbar-nav desktop-nav">
        {navItems.map((item) => (
          <Link
            key={item.path}
            to={item.path}
            className={location.pathname === item.path ? "active" : ""}
          >
            {item.label}
          </Link>
        ))}
      </nav>

      {/* Nút hamburger cho mobile */}
      <button
        className="mobile-menu-toggle"
        onClick={toggleMobileNav}
        aria-label="Toggle mobile menu"
      >
        <span className={`hamburger-line ${showMobileNav ? 'active' : ''}`}></span>
        <span className={`hamburger-line ${showMobileNav ? 'active' : ''}`}></span>
        <span className={`hamburger-line ${showMobileNav ? 'active' : ''}`}></span>
      </button>
      {/* Thông tin user và dropdown menu cho desktop */}
      <div className="navbar-actions desktop-actions" ref={dropdownRef}>
        <div className="user-info">
          <span className="user-name">
            {userName}
          </span>
          <div
            className="member-avatar-wrapper"
            onClick={() => setShowMenu((prev) => !prev)}
          >
            {userName.charAt(0).toUpperCase()}
            {/* Hiển thị dot thông báo nếu có thông báo chưa đọc */}
            {unreadCount > 0 && (
              <span className="avatar-notification-dot"></span>
            )}
          </div>
        </div>

        {/* Dropdown menu */}
        {showMenu && (
          <div className="member-dropdown-menu">
            <Link to="/member/activity-history" className="dropdown-item" onClick={handleMenuItemClick}>
              Lịch sử hoạt động
            </Link>
            <Link to="/member/notifications" className="dropdown-item" onClick={handleMenuItemClick}>
              <span>Thông báo cá nhân</span>
              {/* Badge hiển thị số thông báo chưa đọc */}
              {unreadCount > 0 && (
                <span className="notification-badge">{unreadCount > 99 ? '99+' : unreadCount}</span>
              )}
            </Link>
            <Link to="/member/profile" className="dropdown-item" onClick={handleMenuItemClick}>
              Hồ sơ cá nhân
            </Link>
            <Link to="/member/change-password" className="dropdown-item" onClick={handleMenuItemClick}>
              Đổi mật khẩu
            </Link>

            <div className="dropdown-divider"></div>

            <button onClick={handleLogout} className="dropdown-item logout-btn">
              Đăng xuất
            </button>
          </div>
        )}
      </div>

      {/* Menu navigation cho mobile */}
      <div
        className={`mobile-nav-overlay ${showMobileNav ? 'active' : ''}`}
        ref={mobileNavRef}
      >
        <nav className="mobile-nav">
          {/* Header với thông tin user */}
          <div className="mobile-nav-header">
            <div className="mobile-user-info">
              <div className="mobile-avatar">
                {userName.charAt(0).toUpperCase()}
                {/* Hiển thị dot thông báo nếu có thông báo chưa đọc */}
                {unreadCount > 0 && (
                  <span className="avatar-notification-dot"></span>
                )}
              </div>
              <span className="mobile-user-name">{userName}</span>
            </div>
          </div>

          {/* Danh sách menu items */}
          <div className="mobile-nav-items">
            {/* Menu điều hướng chính */}
            {navItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`mobile-nav-item ${location.pathname === item.path ? "active" : ""}`}
                onClick={handleMobileNavItemClick}
              >
                {item.label}
              </Link>
            ))}

            <div className="mobile-nav-divider"></div>

            {/* Menu user actions */}
            <Link to="/member/activity-history" className="mobile-nav-item" onClick={handleMobileNavItemClick}>
              Lịch sử hoạt động
            </Link>
            <Link to="/member/notifications" className="mobile-nav-item" onClick={handleMobileNavItemClick}>
              <span>Thông báo cá nhân</span>
              {/* Badge hiển thị số thông báo chưa đọc */}
              {unreadCount > 0 && (
                <span className="notification-badge">{unreadCount > 99 ? '99+' : unreadCount}</span>
              )}
            </Link>
            <Link to="/member/profile" className="mobile-nav-item" onClick={handleMobileNavItemClick}>
              Hồ sơ cá nhân
            </Link>
            <Link to="/member/change-password" className="mobile-nav-item" onClick={handleMobileNavItemClick}>
              Đổi mật khẩu
            </Link>

            <div className="mobile-nav-divider"></div>

            {/* Nút đăng xuất */}
            <button onClick={handleLogout} className="mobile-nav-item logout-btn">
              Đăng xuất
            </button>
          </div>
        </nav>
      </div>
    </header>
  );
};

export default MemberNavbar;
