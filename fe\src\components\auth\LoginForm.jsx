import React, { useState, useEffect } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { validateEmail } from "../../utils/validation";
import "../../styles/components/LoginForm.scss";
import authService from "../../services/authService";
import securityService from "../../services/securityService";
import SuspendedAccountModal from "./SuspendedAccountModal";
import { FaEye, FaEyeSlash } from "react-icons/fa";

export default function LoginForm() {
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });
  const [error, setError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [showSuspendedModal, setShowSuspendedModal] = useState(false);
  const [suspendedEmail, setSuspendedEmail] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // Check for success message from registration
  useEffect(() => {
    if (location.state?.message) {
      setSuccessMessage(location.state.message);
      if (location.state?.email) {
        setFormData((prev) => ({ ...prev, email: location.state.email }));
      }
      // Clear the state to prevent showing message on refresh
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    setError("");
    setSuccessMessage("");
    setShowSuspendedModal(false);
  };

  const handleCloseSuspendedModal = () => {
    setShowSuspendedModal(false);
    setSuspendedEmail("");
  };



  const togglePasswordVisibility = () => {
    setShowPassword((prev) => !prev);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.email || !formData.password) {
      setError("Vui lòng nhập đầy đủ email và mật khẩu");
      return;
    }

    if (!validateEmail(formData.email) || formData.password.length < 6) {
      setError("Mật khẩu hoặc email nhập chưa đúng");
      return;
    }

    // Check if user is blocked before attempting login
    const blockStatus = securityService.isUserBlocked(formData.email);
    if (blockStatus.blocked) {
      setError(blockStatus.message);
      return;
    }

    setIsLoading(true);
    try {
      const result = await authService.login(formData.email, formData.password);

      if (result.success) {
        // Track successful login
        securityService.trackLoginAttempt(formData.email, true);
        const redirectPath = authService.getRedirectPath();
        navigate(redirectPath);
      } else {
        // Handle different types of login failures with specific messages

        // 1. Handle suspended/inactive account (status = 0) - 401 Unauthorized
        if (result.suspended || result.errorType === "account_suspended" || result.statusCode === 401) {
          setSuspendedEmail(formData.email);
          setShowSuspendedModal(true);
          setError("Tài khoản đã bị đình chỉ hoạt động. Vui lòng liên hệ quản trị viên để được hỗ trợ.");
          return;
        }

        // 2. Handle invalid credentials (wrong email/password) - 400 Bad Request
        if (result.errorType === "invalid_credentials" || result.statusCode === 400) {
          setError("Email hoặc mật khẩu không đúng. Vui lòng kiểm tra lại thông tin đăng nhập.");
          return;
        }

        // 3. Handle banned account
        if (result.errorType === "account_banned" || result.error === "Account is banned") {
          setError("Tài khoản đã bị cấm vĩnh viễn. Vui lòng liên hệ quản trị viên.");
          return;
        }

        // 4. Handle access denied - 403 Forbidden
        if (result.errorType === "access_denied" || result.statusCode === 403) {
          setError("Bạn không có quyền truy cập hệ thống. Vui lòng liên hệ quản trị viên.");
          return;
        }

        // 5. Handle server errors - 500 Internal Server Error
        if (result.errorType === "server_error" || result.statusCode === 500) {
          setError("Máy chủ đang gặp sự cố. Vui lòng thử lại sau ít phút.");
          return;
        }

        // 6. Track failed login attempt for security
        const attemptResult = securityService.trackLoginAttempt(
          formData.email,
          false
        );

        // 7. Handle security blocking (too many attempts)
        if (attemptResult.blocked) {
          setError(`Tài khoản tạm thời bị khóa: ${attemptResult.message}`);
          return;
        }

        // 8. Default error message with attempt counter
        const errorMessage = attemptResult.attemptsRemaining
          ? `Đăng nhập thất bại: ${attemptResult.message}`
          : `Đăng nhập thất bại: ${result.error || "Thông tin đăng nhập không chính xác"}`;

        setError(errorMessage);
      }
    } catch (error) {
      console.error("Error during login:", error);

      // Handle specific HTTP status codes with clear messages
      if (error.response) {
        const status = error.response.status;
        const responseData = error.response.data;

        // Prioritize status code-based error handling for consistency

        if (status === 400) {
          // 400 Bad Request - Invalid credentials (wrong email/password)
          setError("Email hoặc mật khẩu không đúng. Vui lòng kiểm tra lại thông tin đăng nhập.");
        } else if (status === 401) {
          // 401 Unauthorized - Account suspended/inactive (status = 0)
          setSuspendedEmail(formData.email);
          setShowSuspendedModal(true);
          setError("Tài khoản đã bị đình chỉ hoạt động. Vui lòng liên hệ quản trị viên để được hỗ trợ.");
        } else if (status === 403) {
          if (responseData?.message === "Account is banned" ||
            responseData?.message?.includes("banned")) {
            setError("Tài khoản đã bị cấm vĩnh viễn. Vui lòng liên hệ quản trị viên.");
          } else {
            setError("Bạn không có quyền truy cập hệ thống. Vui lòng liên hệ quản trị viên.");
          }
        } else if (status === 500) {
          setError("Máy chủ đang gặp sự cố. Vui lòng thử lại sau ít phút.");
        } else if (status === 404) {
          setError("Không tìm thấy tài khoản với email này. Vui lòng kiểm tra lại email.");
        } else {
          setError(
            `Đăng nhập thất bại: ${responseData?.message ||
            "Đã xảy ra lỗi không xác định. Vui lòng thử lại sau."}`
          );
        }
      } else if (error.request) {
        setError("Đăng nhập thất bại: Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng.");
      } else {
        setError("Đăng nhập thất bại: Đã xảy ra lỗi hệ thống. Vui lòng thử lại sau.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    setIsGoogleLoading(true);
    setError("");

    try {
      const result = await authService.googleLogin();

      if (!result.success) {
        setError(result.error || "Không thể khởi tạo đăng nhập Google");
      }
      // If successful, user will be redirected to Google OAuth page
    } catch (error) {
      console.error("Google login error:", error);
      setError("Đã xảy ra lỗi khi khởi tạo đăng nhập Google");
    } finally {
      setIsGoogleLoading(false);
    }
  };

  return (
    <div className="login-form__container">
      <div className="login-form__box">
        <div className="login-form__logo">LOGO</div>
        <div className="login-form__welcome">CHÀO MỪNG BẠN ĐÃ TRỞ LẠI</div>

        {/* Google Login Button */}
        <button
          className="login-form__google-btn"
          type="button"
          onClick={handleGoogleLogin}
          disabled={isGoogleLoading || isLoading}
        >
          <div className="login-form__google-icon">
            <svg width="18" height="18" viewBox="0 0 18 18">
              <path
                fill="#4285F4"
                d="M16.51 8H8.98v3h4.3c-.18 1-.74 1.48-1.6 2.04v2.01h2.6a7.8 7.8 0 0 0 2.38-5.88c0-.57-.05-.66-.15-1.18z"
              />
              <path
                fill="#34A853"
                d="M8.98 17c2.16 0 3.97-.72 5.3-1.94l-2.6-2.04a4.8 4.8 0 0 1-7.18-2.53H1.83v2.07A8 8 0 0 0 8.98 17z"
              />
              <path
                fill="#FBBC05"
                d="M4.5 10.49a4.8 4.8 0 0 1 0-3.07V5.35H1.83a8 8 0 0 0 0 7.28l2.67-2.14z"
              />
              <path
                fill="#EA4335"
                d="M8.98 4.72c1.16 0 2.23.4 3.06 1.2l2.3-2.3A8 8 0 0 0 1.83 5.35L4.5 7.42a4.77 4.77 0 0 1 4.48-2.7z"
              />
            </svg>
          </div>
          {isGoogleLoading ? "ĐANG XỬ LÝ..." : "Đăng nhập với Google"}
        </button>

        {/* Divider */}
        <div className="login-form__divider">
          <span></span>
          <span>hoặc</span>
          <span></span>
        </div>

        <form className="login-form__form" onSubmit={handleSubmit}>
          <label className="login-form__label">EMAIL</label>
          <input
            className="login-form__input"
            type="email"
            name="email"
            value={formData.email}
            onChange={handleInputChange}
            placeholder="Nhập địa chỉ email"
            required
          />

          <label className="login-form__label">MẬT KHẨU</label>
          <div className="password-input-container">
            <input
              className="login-form__input"
              type={showPassword ? "text" : "password"}
              name="password"
              value={formData.password}
              onChange={handleInputChange}
              placeholder="Nhập mật khẩu"
              required
            />
            <button
              type="button"
              className="password-toggle-btn"
              onClick={togglePasswordVisibility}
            >
              {showPassword ? <FaEyeSlash /> : <FaEye />}
            </button>
          </div>

          {successMessage && (
            <div className="login-form__success">{successMessage}</div>
          )}
          {error && <div className="login-form__error">{error}</div>}
          <button
            className="login-form__submit"
            type="submit"
            disabled={isLoading}
          >
            {isLoading ? "ĐANG ĐĂNG NHẬP..." : "ĐĂNG NHẬP"}
          </button>
        </form>

        <div className="login-form__forgot-password">
          <Link to="/forgot-password">Quên mật khẩu?</Link>
        </div>

        <div className="login-form__register">
          <Link to="/register">
            BẠN CHƯA CÓ TÀI KHOẢN? <span>ĐĂNG KÝ</span>
          </Link>
        </div>
      </div>

      {/* Suspended Account Modal */}
      <SuspendedAccountModal
        visible={showSuspendedModal}
        onClose={handleCloseSuspendedModal}
        userEmail={suspendedEmail}
      />
    </div>
  );
}
