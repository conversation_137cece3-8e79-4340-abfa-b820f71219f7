import { useState, useCallback } from "react";
import bloodDonationService from "../services/bloodDonationService";

/**
 * Custom hook để quản lý modal states
 * Cập nhật để lấy thông tin chi tiết từ API
 */
const useActivityModals = () => {
  const [selectedActivity, setSelectedActivity] = useState(null);
  const [showWorkflowModal, setShowWorkflowModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [loadingDetails, setLoadingDetails] = useState(false);

  // View appointment details - cập nhật để lấy thông tin chi tiết từ API
  const handleViewDetails = useCallback(async (activity) => {
    setLoadingDetails(true);

    try {
      // Nếu là donation appointment, lấy thông tin chi tiết từ API
      if (activity.type === "donation" && activity.id) {
        console.log("🔍 Fetching detailed appointment info for ID:", activity.id);

        try {
          const detailedData = await bloodDonationService.getAppointmentDetails(activity.id);
          console.log("✅ Detailed appointment data:", detailedData);

          // Merge thông tin chi tiết vào activity
          const enhancedActivity = {
            ...activity,
            // Cập nhật số lượng máu từ API
            quantity: detailedData.DonationCapacity ? `${detailedData.DonationCapacity}ml` :
              detailedData.donationCapacity ? `${detailedData.donationCapacity}ml` :
                activity.quantity || "450ml",
            // Cập nhật cân nặng và chiều cao từ WeightAppointment và HeightAppointment
            weight: detailedData.WeightAppointment || detailedData.weightAppointment ||
              detailedData.Weight || detailedData.weight || activity.weight || 0,
            height: detailedData.HeightAppointment || detailedData.heightAppointment ||
              detailedData.Height || detailedData.height || activity.height || 0,
            // Cập nhật thông tin sức khỏe chi tiết
            healthCheck: {
              heartRate: detailedData.HeartRate || detailedData.heartRate || activity.healthCheck?.heartRate || "",
              bloodPressure: detailedData.BloodPressure || detailedData.bloodPressure || activity.healthCheck?.bloodPressure || "",
              hemoglobin: detailedData.Hemoglobin || detailedData.hemoglobin || activity.healthCheck?.hemoglobin || "",
              temperature: detailedData.Temperature || detailedData.temperature || activity.healthCheck?.temperature || "",
              // Sử dụng WeightAppointment và HeightAppointment cho healthCheck
              weight: detailedData.WeightAppointment || detailedData.weightAppointment ||
                detailedData.Weight || detailedData.weight || activity.weight || 0,
              height: detailedData.HeightAppointment || detailedData.heightAppointment ||
                detailedData.Height || detailedData.height || activity.height || 0,
            },
            // Cập nhật ghi chú bác sĩ
            doctorNotes: detailedData.DoctorNotes || detailedData.doctorNotes || activity.doctorNotes || "",
            notes: detailedData.Notes || detailedData.notes || activity.notes || "",
          };

          setSelectedActivity(enhancedActivity);
        } catch (apiError) {
          console.warn("⚠️ Could not fetch detailed appointment info, using existing data:", apiError);
          setSelectedActivity(activity);
        }
      } else {
        // Cho blood requests hoặc khi không có ID, sử dụng dữ liệu hiện có
        setSelectedActivity(activity);
      }

      setShowDetailModal(true);
    } catch (error) {
      console.error("❌ Error in handleViewDetails:", error);
      setSelectedActivity(activity);
      setShowDetailModal(true);
    } finally {
      setLoadingDetails(false);
    }
  }, []);

  // View workflow - cập nhật để lấy thông tin chi tiết từ API
  const handleViewWorkflow = useCallback(async (activity) => {
    setLoadingDetails(true);

    try {
      // Nếu là donation appointment, lấy thông tin chi tiết từ API để có dữ liệu mới nhất
      if (activity.type === "donation" && activity.id) {
        console.log("🔍 Fetching latest appointment info for workflow view, ID:", activity.id);

        try {
          const detailedData = await bloodDonationService.getAppointmentDetails(activity.id);
          console.log("✅ Latest appointment data for workflow:", detailedData);

          // Tạo enhanced activity với thông tin mới nhất từ API
          const enhancedActivity = {
            ...activity,
            // Cập nhật trạng thái và quy trình mới nhất
            status: detailedData.Status !== undefined ? detailedData.Status : detailedData.status || activity.status,
            process: detailedData.Process !== undefined ? detailedData.Process : detailedData.process || activity.process,
            // Cập nhật thông tin sức khỏe mới nhất
            bloodPressure: detailedData.BloodPressure || detailedData.bloodPressure || activity.bloodPressure || "",
            heartRate: detailedData.HeartRate || detailedData.heartRate || activity.heartRate || 0,
            hemoglobin: detailedData.Hemoglobin || detailedData.hemoglobin || activity.hemoglobin || 0,
            temperature: detailedData.Temperature || detailedData.temperature || activity.temperature || 0,
            // Cập nhật ghi chú bác sĩ
            doctorNotes: detailedData.DoctorNotes || detailedData.doctorNotes || activity.doctorNotes || "",
            notes: detailedData.Notes || detailedData.notes || activity.notes || "",
            // Cập nhật thông tin appointment
            appointmentDate: detailedData.AppointmentDate || detailedData.appointmentDate || activity.appointmentDate,
            timeSlot: detailedData.TimeSlot || detailedData.timeSlot || activity.timeSlot,
            // Cập nhật thông tin người dùng nếu có
            userId: detailedData.UserId || detailedData.userId || activity.userId,
            // Thêm thông tin chi tiết khác
            donationCapacity: detailedData.DonationCapacity || detailedData.donationCapacity || activity.donationCapacity || 450,
            weight: detailedData.WeightAppointment || detailedData.weight || activity.weight || 0,
            height: detailedData.HeightAppointment || detailedData.height || activity.height || 0,
          };

          setSelectedActivity(enhancedActivity);
        } catch (apiError) {
          console.warn("⚠️ Could not fetch latest appointment info for workflow, using existing data:", apiError);
          setSelectedActivity(activity);
        }
      } else {
        // Cho blood requests hoặc khi không có ID, sử dụng dữ liệu hiện có
        setSelectedActivity(activity);
      }

      setShowWorkflowModal(true);
    } catch (error) {
      console.error("❌ Error in handleViewWorkflow:", error);
      setSelectedActivity(activity);
      setShowWorkflowModal(true);
    } finally {
      setLoadingDetails(false);
    }
  }, []);

  // Close modals
  const closeDetailModal = useCallback(() => {
    setShowDetailModal(false);
    setSelectedActivity(null);
  }, []);

  const closeWorkflowModal = useCallback(() => {
    setShowWorkflowModal(false);
    setSelectedActivity(null);
  }, []);

  return {
    // State
    selectedActivity,
    showWorkflowModal,
    showDetailModal,
    loadingDetails,

    // Actions
    handleViewDetails,
    handleViewWorkflow,
    closeDetailModal,
    closeWorkflowModal,
    setSelectedActivity,
    setShowWorkflowModal,
    setShowDetailModal,
  };
};

export default useActivityModals;
