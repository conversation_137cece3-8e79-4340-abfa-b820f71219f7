import React from "react";
import { Card, Row, Col, Typography, Empty } from "antd";
import { ToolOutlined } from "@ant-design/icons";
import AdminLayout from "../../components/admin/AdminLayout";

const { Title, Paragraph } = Typography;

const AdminToolsPage = () => {
  return (
    <AdminLayout
      pageTitle="Công cụ hệ thống"
      pageDescription="Các công cụ hỗ trợ quản trị và bảo trì hệ thống"
      pageIcon={<ToolOutlined />}
    >
      <Row gutter={[24, 24]}>
        <Col span={24}>
          <Card>
            <Empty
              description={
                <div>
                  <Title level={4}>Không có công cụ khả dụng</Title>
                  <Paragraph>
                    Hiện tại không có công cụ nào được cấu hình. Các công cụ
                    quản trị sẽ được thêm vào khi cần thiết.
                  </Paragraph>
                </div>
              }
            />
          </Card>
        </Col>
      </Row>
    </AdminLayout>
  );
};

export default AdminToolsPage;
