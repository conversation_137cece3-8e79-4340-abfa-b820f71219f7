import React from "react";
import { useNavigate } from "react-router-dom";
import {
  Form,
  Input,
  Radio,
  DatePicker,
  Button,
  Card,
  Row,
  Col,
  Typography,
  Divider,
} from "antd";
import {
  InfoCircleOutlined,
  UserOutlined,
  RightOutlined,
} from "@ant-design/icons";
import AddressForm from "../AddressForm";
import dayjs from "dayjs";

const { Title } = Typography;

/**
 * Component hiển thị bước 1: Thông tin cá nhân
 */
const PersonalInfoStep = ({ personalInfo, onSubmit }) => {
  const navigate = useNavigate();

  const handlePersonalInfoSubmit = () => {
    // Validate required fields
    if (
      !personalInfo.fullName ||
      !personalInfo.phone ||
      !personalInfo.dateOfBirth
    ) {
      alert("Vui lòng điền đầy đủ thông tin bắt buộc");
      return;
    }
    onSubmit();
  };

  return (
    <Card
      title={
        <div className="card-title">
          <UserOutlined className="title-icon" />
          <span>Thông tin cá nhân</span>
        </div>
      }
      className="form-card"
      styles={{
        header: {
          background: "linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)",
          borderRadius: "12px 12px 0 0",
          borderBottom: "2px solid #1890ff",
        },
      }}
    >
      <div className="info-section enhanced-info-section">
        <InfoCircleOutlined style={{ fontSize: 24, marginRight: 14 }} />
        <span>
          <b>Thông tin đã được điền sẵn từ hồ sơ cá nhân.</b>
          <br />
          Các thông tin dưới đây được lấy từ hồ sơ cá nhân của bạn và{" "}
          <b>không thể chỉnh sửa tại đây</b>.
          <br />
          Nếu cần thay đổi, vui lòng cập nhật tại
          <span
            style={{
              color: "#0056b3",
              textDecoration: "underline",
              cursor: "pointer",
              fontWeight: 6000,
              marginLeft: 4,
            }}
            onClick={() => navigate("/member/profile")}
          >
            trang Hồ sơ cá nhân
          </span>
          .
        </span>
      </div>

      <Form layout="vertical">
        <div className="personal-info-header">
          <Title level={4} className="header-title">
            Thông tin cơ bản
          </Title>
        </div>

        <div className="personal-info-section">
          <Row gutter={[24, 16]}>
            <Col xs={24} md={12}>
              <Form.Item
                label={<span className="form-label">Họ và tên</span>}
                required
              >
                <Input
                  value={personalInfo.fullName}
                  disabled
                  placeholder="Nhập họ và tên"
                  className="disabled-input"
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item label={<span className="form-label">Email</span>}>
                <Input
                  value={personalInfo.email}
                  disabled
                  placeholder="Nhập email"
                  className="disabled-input"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={[24, 16]}>
            <Col xs={24} md={12}>
              <Form.Item
                label={<span className="form-label">Số điện thoại</span>}
                required
              >
                <Input
                  value={personalInfo.phone}
                  disabled
                  placeholder="Nhập số điện thoại"
                  className="disabled-input"
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                label={<span className="form-label">Ngày sinh</span>}
                required
              >
                <DatePicker
                  value={
                    personalInfo.dateOfBirth
                      ? dayjs(personalInfo.dateOfBirth)
                      : null
                  }
                  disabled
                  className="disabled-datepicker"
                  placeholder="Chọn ngày sinh"
                  format="DD/MM/YYYY"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label={<span className="form-label"> Giới tính</span>}>
            <Radio.Group
              value={personalInfo.gender}
              disabled
              className="radio-group"
            >
              <Radio value="male" className="radio-item">
                Nam
              </Radio>
              <Radio value="female" className="radio-item">
                Nữ
              </Radio>
              <Radio value="other" className="radio-item">
                Khác
              </Radio>
            </Radio.Group>
          </Form.Item>
        </div>

        <Divider />

        {/* Address Form */}
        <AddressForm
          initialAddress={personalInfo.address}
          onAddressChange={() => { }}
          readOnly={true}
        />

        <div className="submit-section">
          <Button
            type="primary"
            size="large"
            onClick={handlePersonalInfoSubmit}
            icon={<RightOutlined />}
            className="submit-button"
          >
            Tiếp tục đến khảo sát sức khỏe
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default PersonalInfoStep;
