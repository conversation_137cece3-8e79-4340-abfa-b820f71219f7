import React from "react";
import { Card, Empty, Button, Typography } from "antd";
import {
  HeartOutlined,
  MedicineBoxOutlined,
  HistoryOutlined,
} from "@ant-design/icons";

const { Title, Paragraph } = Typography;

/**
 * Component Empty State cho ActivityHistoryPage
 * <PERSON><PERSON><PERSON> nguyên thiết kế và logic từ file gốc
 */
const ActivityEmptyState = ({ filter }) => {
  return (
    <Card className="empty-state">
      <Empty
        image={
          <div className="empty-icon">
            {filter === "donations" ? (
              <HeartOutlined style={{ fontSize: "48px", color: "#d32f2f" }} />
            ) : filter === "requests" ? (
              <MedicineBoxOutlined
                style={{ fontSize: "48px", color: "#1890ff" }}
              />
            ) : (
              <HistoryOutlined
                style={{ fontSize: "48px", color: "#722ed1" }}
              />
            )}
          </div>
        }
        description={
          <div className="empty-content">
            <Title level={3} className="empty-title">
              Ch<PERSON>a có hoạt động nào
            </Title>
            <Paragraph className="empty-description">
              {filter === "donations"
                ? "Bạn chưa có lần hiến máu nào. Hãy bắt đầu hành trình hiến máu cứu người ngay hôm nay!"
                : filter === "requests"
                ? "Bạn chưa có yêu cầu máu nào. Bạn có thể tạo yêu cầu máu khi cần."
                : "Bạn chưa có hoạt động nào."}
            </Paragraph>
            <Button
              type="primary"
              size="large"
              className="empty-action-button"
              icon={
                filter === "donations" ? (
                  <HeartOutlined />
                ) : (
                  <MedicineBoxOutlined />
                )
              }
            >
              {filter === "donations" ? "Đăng ký hiến máu" : "Tạo yêu cầu máu"}
            </Button>
          </div>
        }
      />
    </Card>
  );
};

export default ActivityEmptyState;
