import { Table, Tag, Button } from "antd";
import { getGenderText, GENDER_FILTERS } from "../../../utils/genderUtils";

/**
 * Component bảng danh sách người hiến máu
 */
const DonorTable = ({
  donors,
  loading,
  onUpdateDonor,
  onUpdateStatus,
  onDeleteAppointment
}) => {

  const getTimeSlotText = (slot) => {
    if (slot === "morning" || slot === "Sáng (7:00-12:00)") {
      return "7:00 - 12:00";
    } else if (slot === "afternoon" || slot === "Chiều (13:00-17:00)") {
      return "13:00 - 17:00";
    }
    return slot || "N/A";
  };

  const BLOOD_TYPES = [
    "O+", "O-", "A+", "A-", "B+", "B-", "AB+", "AB-"
  ];





  const TIMESLOT_FILTERS = [
    { text: "Sáng (7:00 - 12:00)", value: "morning" },
    { text: "<PERSON><PERSON><PERSON> (13:00 - 17:00)", value: "afternoon" },
  ];

  // Hàm sắp xếp ngày hẹn theo thứ tự: hôm nay, ngày mai, tương lai, trong quá khứ
  const sortAppointmentDate = (a, b) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const dateA = new Date(a.appointmentDate);
    dateA.setHours(0, 0, 0, 0);

    const dateB = new Date(b.appointmentDate);
    dateB.setHours(0, 0, 0, 0);

    // Xác định loại ngày cho A
    let categoryA;
    if (dateA.getTime() === today.getTime()) {
      categoryA = 1; // Hôm nay
      // } else if (dateA.getTime() === tomorrow.getTime()) {
      //   categoryA = 2; // Ngày mai
    } else if (dateA > today) {
      categoryA = 3; // Tương lai
    } else {
      categoryA = 4; // Trong quá khứ
    }

    // Xác định loại ngày cho B
    let categoryB;
    if (dateB.getTime() === today.getTime()) {
      categoryB = 1; // Hôm nay
      // } else if (dateB.getTime() === tomorrow.getTime()) {
      //   categoryB = 2; // Ngày mai
    } else if (dateB > today) {
      categoryB = 3; // Tương lai
    } else {
      categoryB = 4; // Trong quá khứ
    }

    // Sắp xếp theo category trước
    if (categoryA !== categoryB) {
      return categoryA - categoryB;
    }

    // Nếu cùng category, sắp xếp theo ngày
    if (categoryA === 3) {
      // Tương lai: gần nhất trước
      return dateA - dateB;
    } else if (categoryA === 4) {
      // Quá khứ: gần nhất trước (ngày lớn nhất trước)
      return dateB - dateA;
    }

    // Hôm nay và ngày mai: không cần sắp xếp thêm
    return 0;
  };

  const columns = [
    {
      title: "Mã lịch hẹn",
      dataIndex: "id",
      key: "appointmentId",
      width: 100,
      render: (id) => <span style={{ fontWeight: 'bold', color: '#1890ff' }}>#{id}</span>,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "Tên",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Nhóm máu",
      dataIndex: "bloodType",
      key: "bloodType",
      render: (bloodType, record) => {
        // Try to combine BloodGroup and RhType if available, otherwise use bloodType
        let displayBloodType = bloodType;

        if (record.bloodGroup && record.rhType) {
          // If we have separate fields, combine them
          const rhSymbol = record.rhType.includes('+') ? '+' :
            record.rhType.includes('-') ? '-' :
              record.rhType.replace('Rh', '');
          displayBloodType = `${record.bloodGroup}${rhSymbol}`;
        } else if (record.BloodGroup && record.RhType) {
          // Try uppercase field names
          const rhSymbol = record.RhType.includes('+') ? '+' :
            record.RhType.includes('-') ? '-' :
              record.RhType.replace('Rh', '');
          displayBloodType = `${record.BloodGroup}${rhSymbol}`;
        }

        return <Tag color="red">{displayBloodType || 'N/A'}</Tag>;
      },
      filters: BLOOD_TYPES.map(type => ({ text: type, value: type })),
      onFilter: (value, record) => {
        // Check multiple possible field combinations
        let bloodTypeToCheck = record.bloodType;

        if (record.bloodGroup && record.rhType) {
          const rhSymbol = record.rhType.includes('+') ? '+' :
            record.rhType.includes('-') ? '-' :
              record.rhType.replace('Rh', '');
          bloodTypeToCheck = `${record.bloodGroup}${rhSymbol}`;
        } else if (record.BloodGroup && record.RhType) {
          const rhSymbol = record.RhType.includes('+') ? '+' :
            record.RhType.includes('-') ? '-' :
              record.RhType.replace('Rh', '');
          bloodTypeToCheck = `${record.BloodGroup}${rhSymbol}`;
        }

        return bloodTypeToCheck === value;
      },
    },
    {
      title: "Tuổi",
      dataIndex: "age",
      key: "age",
      sorter: (a, b) => a.age - b.age,
    },
    {
      title: "Giới tính",
      dataIndex: "gender",
      key: "gender",
      render: (g) => getGenderText(g),
      filters: GENDER_FILTERS,
      onFilter: (value, record) => record.gender === value,
    },
    {
      title: "Điện thoại",
      dataIndex: "phone",
      key: "phone",
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
    },
    {
      title: "Ngày đăng ký",
      dataIndex: "createdAt",
      key: "createdAt",
      render: (d) => (d ? new Date(d).toLocaleDateString("vi-VN") : "N/A"),
      sorter: (a, b) => new Date(a.createdAt) - new Date(b.createdAt),
    },
    {
      title: "Ngày hẹn",
      dataIndex: "appointmentDate",
      key: "appointmentDate",
      render: (d) => {
        if (!d) return "N/A";

        const appointmentDate = new Date(d);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        appointmentDate.setHours(0, 0, 0, 0);

        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        let label = "";
        if (appointmentDate.getTime() === today.getTime()) {
          label = " (Hôm nay)";
        }

        return (
          <span>
            {new Date(d).toLocaleDateString("vi-VN")}
            {label && <span style={{ color: '#1890ff', fontWeight: 'bold' }}>{label}</span>}
          </span>
        );
      },
      sorter: sortAppointmentDate,
      defaultSortOrder: 'ascend',
    },
    {
      title: "Quy trình hiến máu",
      dataIndex: "process",
      key: "process",
      render: (process, record) => {
        // Check if cancelled by user (cancel = true is the primary indicator)
        const isCancelled = record.cancel === true || record.cancel === 1 ||
          record.Cancel === true || record.Cancel === 1 ||
          record.cancelled === true || record.status === 3 || record.isCancelled === true;

        // Check if rejected (status = false or 1)
        const isRejected = record.status === false || record.status === 1;

        if (isCancelled) {
          // Show cancelled status with reason
          return (
            <div>
              <Tag color="volcano">Đã hủy bởi người đăng ký</Tag>
              {record.cancelledAt && (
                <div style={{ fontSize: '12px', color: '#666', marginTop: '2px', maxWidth: '200px' }}>
                  Hủy lúc: {new Date(record.cancelledAt).toLocaleString("vi-VN")}
                </div>
              )}
              {record.cancelReason && (
                <div style={{ fontSize: '12px', color: '#666', marginTop: '2px', maxWidth: '200px' }}>
                  Lý do: {record.cancelReason}
                </div>
              )}
            </div>
          );
        } else if (isRejected) {
          // Show rejection with notes only when rejected
          return (
            <div>
              <Tag color="red">Không chấp nhận</Tag>
              {record.notes && (
                <div style={{ fontSize: '12px', color: '#666', marginTop: '2px', maxWidth: '200px' }}>
                  {record.notes}
                </div>
              )}
            </div>
          );
        } else {
          // Show process steps for all other cases (approved, pending, etc.)
          const processSteps = {
            1: "Đăng ký",
            2: "Khám sức khỏe cơ bản",
            3: "Lấy máu",
            4: "Xét nghiệm máu",
            5: "Nhập kho"
          };

          // Use actual process from record, not default
          const currentProcess = record.process || record.Process || process || 1;
          const processText = processSteps[currentProcess] || "Đăng ký";

          // Color coding for different steps
          const stepColors = {
            1: "blue",      // Đăng ký
            2: "orange",    // Khám sức khỏe cơ bản
            3: "purple",    // Lấy máu
            4: "cyan",      // Xét nghiệm máu
            5: "green"      // Nhập kho
          };

          return (
            <Tag color={stepColors[currentProcess] || "blue"}>
              Bước {currentProcess}: {processText}
            </Tag>
          );
        }
      },
      filters: [
        { text: "Khám sức khỏe cơ bản", value: 2 },
        { text: "Lấy máu", value: 3 },
        { text: "Xét nghiệm máu", value: 4 },
        { text: "Nhập kho", value: 5 },
        { text: "Không chấp nhận", value: "rejected" },
      ],
      onFilter: (value, record) => {
        if (value === "rejected") {
          return record.status === false || record.status === 1;
        }
        const currentProcess = record.process || record.Process || 1;
        return currentProcess === value;
      },
    },
    {
      title: "Giờ hẹn",
      dataIndex: "timeSlot",
      key: "timeSlot",
      render: (t) => <Tag color="blue">{getTimeSlotText(t)}</Tag>,
      filters: TIMESLOT_FILTERS,
      onFilter: (value, record) => record.timeSlot === value,
    },
    {
      title: "Hành động",
      key: "actions",
      render: (_, donor) => {
        // Check if cancelled by user
        const isCancelled = donor.cancel === true || donor.cancel === 1 ||
          donor.Cancel === true || donor.Cancel === 1 ||
          donor.cancelled === true || donor.status === 3 || donor.isCancelled === true;

        return (
          <div style={{ display: 'flex', gap: '4px', flexWrap: 'wrap' }}>
            <Button
              type="link"
              size="small"
              onClick={() => onUpdateDonor(donor)}
              disabled={isCancelled}
              style={isCancelled ? { opacity: 0.5 } : {}}
            >
              Thông tin
            </Button>
            <Button
              type="link"
              size="small"
              onClick={() => onUpdateStatus(donor)}
              disabled={isCancelled}
              style={isCancelled ? { opacity: 0.5 } : {}}
            >
              Trạng thái
            </Button>
            {isCancelled && (
              <span style={{ fontSize: '12px', color: '#999', alignSelf: 'center' }}>
                (Đã hủy)
              </span>
            )}
          </div>
        );
      },
    },
  ];

  return (
    <Table
      dataSource={donors}
      columns={columns}
      rowKey="id"
      loading={loading}
      pagination={{ pageSize: 8 }}
      sortDirections={['ascend', 'descend']}
    />
  );
};

export default DonorTable;
